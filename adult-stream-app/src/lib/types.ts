// Video-related types
export interface Video {
  id: string;
  title: string;
  thumbnail: string;
  duration: number; // in seconds
  views: number;
  uploadedAt: Date;
  category: string;
  isHD?: boolean;
  isPremium?: boolean;
  description?: string;
  tags?: string[];
}

// Category-related types
export interface Category {
  name: string;
  slug: string;
  image: string;
  description?: string;
  videoCount?: number;
}

// Filter and sort types
export type SortOption = 'recent' | 'popular' | 'duration' | 'views';
export type FilterOption = 'all' | 'hd' | 'premium';

// Category page data structure
export interface CategoryPageData {
  category: Category;
  videos: Video[];
  totalCount: number;
  hasMore: boolean;
}

// API response types
export interface CategoryVideosResponse {
  videos: Video[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  filters: {
    sort: SortOption;
    filter: FilterOption;
  };
}

// Component prop types
export interface VideoCardProps {
  video: Video;
  className?: string;
  onClick?: () => void;
  showCategory?: boolean;
}

export interface CategoryGridProps {
  videos: Video[];
  loading?: boolean;
  onLoadMore?: () => void;
  hasMore?: boolean;
}

export interface CategoryTabsProps {
  activeTab: SortOption;
  onTabChange: (tab: SortOption) => void;
  className?: string;
}

// Utility types
export type CategorySlug = 
  | 'big-tits'
  | 'milf'
  | 'pov'
  | 'blonde'
  | 'brunette'
  | 'redhead'
  | 'asian'
  | 'black'
  | 'latina'
  | 'amateur'
  | 'teen'
  | 'mature'
  | 'fetish'
  | 'gay'
  | 'lesbian'
  | 'transgender';

// Search and navigation types
export interface SearchResult {
  videos: Video[];
  categories: Category[];
  totalResults: number;
  query: string;
}

export interface NavigationItem {
  label: string;
  href: string;
  isActive?: boolean;
}
