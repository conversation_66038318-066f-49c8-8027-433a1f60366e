'use client';

import React from 'react';
import { CategoryHeader } from './CategoryHeader';

interface CategoryLayoutProps {
  children: React.ReactNode;
  className?: string;
}

export function CategoryLayout({ children, className }: CategoryLayoutProps) {
  return (
    <div
      className={`relative flex size-full min-h-screen flex-col group/design-root overflow-x-hidden bg-[#1A0B0C] dark ${className || ''}`}
      style={{ fontFamily: '"Plus Jakarta Sans", "Noto Sans", sans-serif' }}
    >
      <div className="layout-container flex h-full grow flex-col">
        {/* Header */}
        <CategoryHeader />

        {/* Main Content */}
        <main className="flex flex-1 justify-center py-5 px-4 sm:px-6 lg:px-8">
          <div className="layout-content-container w-full max-w-screen-xl flex-col">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
