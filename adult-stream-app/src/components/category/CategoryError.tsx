'use client';

import React from 'react';
import Link from 'next/link';

interface CategoryErrorProps {
  error?: Error;
  reset?: () => void;
  categoryName?: string;
}

export function CategoryError({ error, reset, categoryName }: CategoryErrorProps) {
  return (
    <div className="flex flex-col items-center justify-center min-h-[400px] text-center p-8">
      {/* Error Icon */}
      <div className="mb-6">
        <svg 
          className="w-20 h-20 text-red-500 mx-auto" 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={1.5} 
            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" 
          />
        </svg>
      </div>

      {/* Error Message */}
      <h2 className="text-2xl font-bold text-white mb-4">
        {categoryName ? `Error Loading ${categoryName} Category` : 'Something went wrong'}
      </h2>
      
      <p className="text-gray-400 mb-8 max-w-md">
        {error?.message || 'We encountered an error while loading this category. Please try again or browse other categories.'}
      </p>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-4">
        {reset && (
          <button
            onClick={reset}
            className="bg-[#E92933] hover:bg-[#d9202a] text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200"
          >
            Try Again
          </button>
        )}
        
        <Link
          href="/categories"
          className="bg-[#391D1F] hover:bg-[#4A2528] text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200"
        >
          Browse Categories
        </Link>
        
        <Link
          href="/"
          className="text-gray-400 hover:text-white font-medium py-3 px-6 transition-colors duration-200"
        >
          Go Home
        </Link>
      </div>

      {/* Additional Help */}
      <div className="mt-8 text-sm text-gray-500">
        <p>If this problem persists, please contact our support team.</p>
      </div>
    </div>
  );
}
