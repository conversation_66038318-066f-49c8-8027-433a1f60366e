'use client';

import React from 'react';
import { CategoryTitle } from './CategoryTitle';
import { CategoryTabs } from './CategoryTabs';
import { CategoryGrid } from './CategoryGrid';
import { useCategoryFilter } from '@/hooks/useCategoryFilter';
import { Video, Category } from '@/lib/types';

interface CategoryPageContentProps {
  category: Category;
  videos: Video[];
}

export function CategoryPageContent({ category, videos }: CategoryPageContentProps) {
  // Use filtering hook
  const { filteredVideos, activeSort, setActiveSort, isLoading } = useCategoryFilter({
    videos,
    initialSort: 'recent'
  });

  return (
    <>
      {/* Category Title and Breadcrumb */}
      <CategoryTitle 
        categoryName={category.name}
        videoCount={filteredVideos.length}
      />

      {/* Category Tabs */}
      <CategoryTabs 
        activeTab={activeSort}
        onTabChange={setActiveSort}
      />

      {/* Video Grid */}
      <CategoryGrid 
        videos={filteredVideos}
        loading={isLoading}
        hasMore={false} // For now, no pagination
      />
    </>
  );
}
