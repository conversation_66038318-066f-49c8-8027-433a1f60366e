'use client';

import React, { useMemo } from 'react';
import { Video } from '@/lib/types';
import { CategoryVideoCard } from './CategoryVideoCard';

interface CategoryGridProps {
  videos: Video[];
  loading?: boolean;
  onLoadMore?: () => void;
  hasMore?: boolean;
  className?: string;
}

// Loading skeleton component
function VideoCardSkeleton() {
  return (
    <div className="aspect-video bg-gray-700 rounded-lg animate-pulse">
      <div className="w-full h-full bg-gradient-to-t from-gray-800 to-gray-700 rounded-lg">
        {/* Skeleton content */}
        <div className="absolute bottom-2 right-2 bg-gray-600 w-12 h-5 rounded animate-pulse" />
        <div className="absolute bottom-3 left-3 space-y-1">
          <div className="bg-gray-600 h-4 w-24 rounded animate-pulse" />
          <div className="bg-gray-600 h-3 w-16 rounded animate-pulse" />
        </div>
      </div>
    </div>
  );
}

// Empty state component
function EmptyState() {
  return (
    <div className="col-span-full flex flex-col items-center justify-center py-12 text-center">
      <div className="mb-4">
        <svg className="w-16 h-16 text-gray-600 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
        </svg>
      </div>
      <h3 className="text-lg font-semibold text-white mb-2">No videos found</h3>
      <p className="text-gray-400 max-w-sm">
        There are no videos in this category yet. Check back later for new content.
      </p>
    </div>
  );
}

export function CategoryGrid({
  videos,
  loading = false,
  onLoadMore,
  hasMore = false,
  className
}: CategoryGridProps) {
  // Memoize video cards to prevent unnecessary re-renders
  const videoCards = useMemo(() => {
    return videos.map((video) => (
      <CategoryVideoCard
        key={video.id}
        video={video}
      />
    ));
  }, [videos]);
  // Show loading skeletons
  if (loading && videos.length === 0) {
    return (
      <div className={`grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4 p-4 ${className || ''}`}>
        {Array.from({ length: 12 }).map((_, index) => (
          <VideoCardSkeleton key={index} />
        ))}
      </div>
    );
  }

  // Show empty state
  if (!loading && videos.length === 0) {
    return (
      <div className={`grid grid-cols-1 p-4 ${className || ''}`}>
        <EmptyState />
      </div>
    );
  }

  return (
    <div className={className}>
      {/* Video Grid */}
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4 p-4">
        {videoCards}

        {/* Loading more skeletons */}
        {loading && videos.length > 0 && (
          <>
            {Array.from({ length: 6 }).map((_, index) => (
              <VideoCardSkeleton key={`loading-${index}`} />
            ))}
          </>
        )}
      </div>

      {/* Load More Button */}
      {!loading && hasMore && onLoadMore && (
        <div className="flex justify-center p-4">
          <button
            onClick={onLoadMore}
            className="bg-[#E92933] hover:bg-[#d9202a] text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200"
          >
            Load More Videos
          </button>
        </div>
      )}

      {/* End of content indicator */}
      {!loading && !hasMore && videos.length > 0 && (
        <div className="text-center p-4">
          <p className="text-gray-400 text-sm">
            You've reached the end of this category
          </p>
        </div>
      )}
    </div>
  );
}
