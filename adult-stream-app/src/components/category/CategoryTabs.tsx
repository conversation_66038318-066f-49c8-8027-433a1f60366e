'use client';

import React from 'react';
import { SortOption } from '@/lib/types';

interface CategoryTabsProps {
  activeTab: SortOption;
  onTabChange: (tab: SortOption) => void;
  className?: string;
}

export function CategoryTabs({ activeTab, onTabChange, className }: CategoryTabsProps) {
  const tabs: { key: SortOption; label: string }[] = [
    { key: 'recent', label: 'Recent' },
    { key: 'popular', label: 'Popular' }
  ];

  return (
    <div className={`pb-3 ${className || ''}`}>
      <div className="flex border-b border-[#4A2528] px-4 gap-6 sm:gap-8">
        {tabs.map((tab) => (
          <button
            key={tab.key}
            onClick={() => onTabChange(tab.key)}
            className={`flex flex-col items-center justify-center border-b-[3px] pb-3 pt-4 transition-colors ${
              activeTab === tab.key
                ? 'border-[#E92933] text-white'
                : 'border-transparent text-[#C89295] hover:border-[#E92933]/50 hover:text-white'
            }`}
          >
            <p className="text-sm font-semibold leading-normal tracking-[0.015em]">
              {tab.label}
            </p>
          </button>
        ))}
      </div>
    </div>
  );
}
