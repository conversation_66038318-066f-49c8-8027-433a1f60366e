'use client';

import React from 'react';
import Link from 'next/link';

interface CategoryTitleProps {
  categoryName: string;
  videoCount?: number;
  className?: string;
}

export function CategoryTitle({ categoryName, videoCount, className }: CategoryTitleProps) {
  return (
    <div className={`flex flex-wrap items-center justify-between gap-4 p-4 ${className || ''}`}>
      <div className="flex flex-col gap-2">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm">
          <Link 
            href="/" 
            className="text-gray-400 hover:text-white transition-colors"
          >
            Home
          </Link>
          <span className="text-gray-600">/</span>
          <Link 
            href="/categories" 
            className="text-gray-400 hover:text-white transition-colors"
          >
            Categories
          </Link>
          <span className="text-gray-600">/</span>
          <span className="text-white font-medium">{categoryName}</span>
        </nav>

        {/* Category Title */}
        <div className="flex items-center gap-3">
          <h1 className="text-white text-3xl font-bold leading-tight tracking-tight">
            Category: {categoryName}
          </h1>
          {videoCount !== undefined && (
            <span className="bg-[#391D1F] text-gray-300 text-sm font-medium px-3 py-1 rounded-full">
              {videoCount} {videoCount === 1 ? 'video' : 'videos'}
            </span>
          )}
        </div>
      </div>

      {/* Optional: Category Actions */}
      <div className="flex items-center gap-2">
        {/* Subscribe/Follow Button */}
        <button className="bg-[#E92933] hover:bg-[#d9202a] text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200">
          Follow Category
        </button>
        
        {/* More Options */}
        <button 
          className="text-gray-400 hover:text-white p-2 rounded-lg hover:bg-[#391D1F] transition-colors"
          aria-label="More options"
        >
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
          </svg>
        </button>
      </div>
    </div>
  );
}
