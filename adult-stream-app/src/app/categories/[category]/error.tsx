'use client';

import React from 'react';
import { CategoryLayout } from '@/components/category/CategoryLayout';
import { CategoryError } from '@/components/category/CategoryError';

interface ErrorPageProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function CategoryErrorPage({ error, reset }: ErrorPageProps) {
  // Log error for debugging
  React.useEffect(() => {
    console.error('Category page error:', error);
  }, [error]);

  return (
    <CategoryLayout>
      <CategoryError 
        error={error}
        reset={reset}
      />
    </CategoryLayout>
  );
}
