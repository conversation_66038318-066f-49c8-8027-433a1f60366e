import React from 'react';
import Link from 'next/link';
import { CategoryLayout } from '@/components/category/CategoryLayout';

export default function CategoryNotFound() {
  return (
    <CategoryLayout>
      <div className="flex flex-col items-center justify-center min-h-[400px] text-center p-8">
        {/* 404 Icon */}
        <div className="mb-6">
          <svg 
            className="w-20 h-20 text-gray-600 mx-auto" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={1.5} 
              d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.29-1.009-5.674-2.326M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" 
            />
          </svg>
        </div>

        {/* Error Message */}
        <h1 className="text-4xl font-bold text-white mb-4">
          Category Not Found
        </h1>
        
        <p className="text-gray-400 mb-8 max-w-md">
          The category you're looking for doesn't exist or may have been moved. 
          Browse our available categories below.
        </p>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4">
          <Link
            href="/categories"
            className="bg-[#E92933] hover:bg-[#d9202a] text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200"
          >
            Browse All Categories
          </Link>
          
          <Link
            href="/"
            className="bg-[#391D1F] hover:bg-[#4A2528] text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200"
          >
            Go Home
          </Link>
        </div>

        {/* Popular Categories */}
        <div className="mt-12 w-full max-w-2xl">
          <h3 className="text-lg font-semibold text-white mb-4">Popular Categories</h3>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
            {['Big Tits', 'MILF', 'Blonde', 'Brunette', 'Asian', 'Amateur', 'Teen', 'Mature'].map((category) => (
              <Link
                key={category}
                href={`/categories/${category.toLowerCase().replace(/\s+/g, '-')}`}
                className="bg-[#391D1F] hover:bg-[#4A2528] text-white text-sm font-medium py-2 px-3 rounded-lg transition-colors duration-200 text-center"
              >
                {category}
              </Link>
            ))}
          </div>
        </div>
      </div>
    </CategoryLayout>
  );
}
