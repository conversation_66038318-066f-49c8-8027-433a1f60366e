import React from 'react';

export default function CategoryLoading() {
  return (
    <div
      className="relative flex size-full min-h-screen flex-col group/design-root overflow-x-hidden bg-[#1A0B0C] dark"
      style={{ fontFamily: '"Plus Jakarta Sans", "Noto Sans", sans-serif' }}
    >
      <div className="layout-container flex h-full grow flex-col">
        {/* Header skeleton */}
        <header className="flex items-center justify-between whitespace-nowrap border-b border-solid border-[#391D1F] px-4 sm:px-6 lg:px-10 py-3">
          <div className="flex items-center gap-4 md:gap-8">
            <div className="flex items-center gap-2">
              <div className="h-6 w-6 bg-[#E92933] rounded animate-pulse" />
              <div className="h-6 w-20 bg-gray-700 rounded animate-pulse" />
            </div>
          </div>
          <div className="flex items-center gap-4">
            <div className="h-10 w-32 bg-gray-700 rounded animate-pulse" />
            <div className="h-10 w-10 bg-gray-700 rounded-full animate-pulse" />
          </div>
        </header>

        {/* Main content skeleton */}
        <main className="flex flex-1 justify-center py-5 px-4 sm:px-6 lg:px-8">
          <div className="layout-content-container w-full max-w-screen-xl flex-col">
            {/* Title skeleton */}
            <div className="flex flex-wrap items-center justify-between gap-4 p-4">
              <div className="h-8 w-48 bg-gray-700 rounded animate-pulse" />
            </div>

            {/* Tabs skeleton */}
            <div className="pb-3">
              <div className="flex border-b border-[#4A2528] px-4 gap-6 sm:gap-8">
                <div className="h-8 w-16 bg-gray-700 rounded animate-pulse" />
                <div className="h-8 w-20 bg-gray-700 rounded animate-pulse" />
              </div>
            </div>

            {/* Grid skeleton */}
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4 p-4">
              {Array.from({ length: 12 }).map((_, index) => (
                <div key={index} className="flex flex-col gap-2">
                  {/* Video thumbnail skeleton */}
                  <div className="aspect-video bg-gray-700 rounded-lg animate-pulse" />
                  {/* Title skeleton */}
                  <div className="h-4 bg-gray-700 rounded animate-pulse" />
                  {/* Metadata skeleton */}
                  <div className="h-3 w-3/4 bg-gray-600 rounded animate-pulse" />
                </div>
              ))}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
