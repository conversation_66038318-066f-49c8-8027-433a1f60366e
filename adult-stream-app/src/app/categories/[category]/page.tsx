import React from 'react';
import { notFound } from 'next/navigation';
import { CategoryLayout } from '@/components/category/CategoryLayout';
import { CategoryPageContent } from '@/components/category/CategoryPageContent';
import { getCategoryBySlug, getVideosByCategory, isValidCategorySlug } from '@/lib/mockData';

interface CategoryPageProps {
  params: Promise<{
    category: string;
  }>;
}

export default async function CategoryPage({ params }: CategoryPageProps) {
  const { category } = await params;

  // Validate category parameter
  if (!isValidCategorySlug(category)) {
    notFound();
  }

  // Get category data
  const categoryData = getCategoryBySlug(category);
  if (!categoryData) {
    notFound();
  }

  // Get videos for this category
  const videos = getVideosByCategory(category);

  return (
    <CategoryLayout>
      <CategoryPageContent
        category={categoryData}
        videos={videos}
      />
    </CategoryLayout>
  );
}
