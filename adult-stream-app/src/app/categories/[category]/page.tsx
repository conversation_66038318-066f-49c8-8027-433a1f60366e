import React from 'react';
import { notFound } from 'next/navigation';
import { Metadata } from 'next';
import { CategoryLayout } from '@/components/category/CategoryLayout';
import { CategoryPageContent } from '@/components/category/CategoryPageContent';
import { getCategoryBySlug, getVideosByCategory, isValidCategorySlug } from '@/lib/mockData';

interface CategoryPageProps {
  params: Promise<{
    category: string;
  }>;
}

// Generate metadata for SEO and social sharing
export async function generateMetadata({ params }: CategoryPageProps): Promise<Metadata> {
  const { category } = await params;

  // Validate category
  if (!isValidCategorySlug(category)) {
    return {
      title: 'Category Not Found - AdultStream',
      description: 'The requested category could not be found.',
    };
  }

  const categoryData = getCategoryBySlug(category);
  if (!categoryData) {
    return {
      title: 'Category Not Found - AdultStream',
      description: 'The requested category could not be found.',
    };
  }

  const videos = getVideosByCategory(category);
  const videoCount = videos.length;

  const title = `${categoryData.name} Videos - AdultStream`;
  const description = `Explore ${videoCount} premium ${categoryData.name.toLowerCase()} videos on AdultStream. High-quality content updated daily with the latest ${categoryData.name.toLowerCase()} videos.`;
  const url = `https://adultstream.com/categories/${category}`;

  return {
    title,
    description,
    keywords: [
      categoryData.name.toLowerCase(),
      'adult videos',
      'streaming',
      'premium content',
      'HD videos',
      'adult entertainment'
    ],
    authors: [{ name: 'AdultStream' }],
    creator: 'AdultStream',
    publisher: 'AdultStream',
    robots: {
      index: false, // Adult content should not be indexed
      follow: false,
    },
    openGraph: {
      type: 'website',
      locale: 'en_US',
      url,
      siteName: 'AdultStream',
      title,
      description,
      images: [
        {
          url: categoryData.image,
          width: 1200,
          height: 630,
          alt: `${categoryData.name} category on AdultStream`,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [categoryData.image],
    },
    alternates: {
      canonical: url,
    },
  };
}

export default async function CategoryPage({ params }: CategoryPageProps) {
  const { category } = await params;

  // Validate category parameter
  if (!isValidCategorySlug(category)) {
    notFound();
  }

  // Get category data
  const categoryData = getCategoryBySlug(category);
  if (!categoryData) {
    notFound();
  }

  // Get videos for this category
  const videos = getVideosByCategory(category);

  return (
    <CategoryLayout>
      <CategoryPageContent
        category={categoryData}
        videos={videos}
      />
    </CategoryLayout>
  );
}
