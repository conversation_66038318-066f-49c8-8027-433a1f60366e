import type { Metada<PERSON> } from "next";
import { Plus_Jakarta_Sans, Noto_Sans } from "next/font/google";
import "./globals.css";

const plusJakartaSans = Plus_Jakarta_Sans({
  variable: "--font-plus-jakarta",
  subsets: ["latin"],
  weight: ["400", "500", "600", "700", "800"],
  display: "swap",
});

const notoSans = Noto_Sans({
  variable: "--font-noto-sans",
  subsets: ["latin"],
  weight: ["400", "500", "700", "900"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "AdultStream - Premium Video Hub",
  description: "Discover premium adult content with AdultStream. Browse categories, watch videos, and enjoy a seamless streaming experience.",
  keywords: ["adult", "streaming", "videos", "entertainment", "premium"],
  authors: [{ name: "AdultStream" }],
  creator: "AdultStream",
  publisher: "AdultStream",
  robots: {
    index: false,
    follow: false,
  },
  viewport: {
    width: "device-width",
    initialScale: 1,
    maximumScale: 1,
  },
  themeColor: "#E92933",
  colorScheme: "dark",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://adultstream.com",
    siteName: "AdultStream",
    title: "AdultStream - Premium Video Hub",
    description: "Discover premium adult content with AdultStream. Browse categories, watch videos, and enjoy a seamless streaming experience.",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "AdultStream",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "AdultStream - Premium Video Hub",
    description: "Discover premium adult content with AdultStream. Browse categories, watch videos, and enjoy a seamless streaming experience.",
    images: ["/og-image.jpg"],
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <head>
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />
        <link rel="icon" href="/favicon.ico" />
        <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
      </head>
      <body
        className={`${plusJakartaSans.variable} ${notoSans.variable} bg-background text-text antialiased`}
      >
        <div className="relative flex min-h-screen flex-col overflow-x-hidden">
          {children}
        </div>
      </body>
    </html>
  );
}
