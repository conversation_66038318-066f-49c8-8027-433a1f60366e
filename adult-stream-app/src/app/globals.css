@import "tailwindcss";

:root {
  /* AdultStream Brand Colors */
  --primary: #E92933;
  --primary-hover: #d9202a;
  --primary-light: #e92933;

  /* Dark Theme Colors */
  --background-primary: #1A080A;
  --background-secondary: #110808;
  --background-tertiary: #1A090A;
  --background-card: #1F1415;
  --background-input: #3A2325;
  --background-hover: #4A2D2F;

  /* Border Colors */
  --border-primary: #3B1D1F;
  --border-secondary: #2A1B1C;
  --border-tertiary: #3A1A1C;

  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #D1A0A3;
  --text-muted: #B88184;
  --text-placeholder: #A9888A;
  --text-disabled: #6B5B5D;

  /* Accent Colors */
  --accent-pink: #e8b4b7;
  --accent-pink-hover: #d9a0a4;

  /* Status Colors */
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;
}

@theme inline {
  /* Colors */
  --color-primary: var(--primary);
  --color-primary-hover: var(--primary-hover);
  --color-primary-light: var(--primary-light);

  --color-background: var(--background-primary);
  --color-background-secondary: var(--background-secondary);
  --color-background-tertiary: var(--background-tertiary);
  --color-background-card: var(--background-card);
  --color-background-input: var(--background-input);
  --color-background-hover: var(--background-hover);

  --color-border: var(--border-primary);
  --color-border-secondary: var(--border-secondary);
  --color-border-tertiary: var(--border-tertiary);

  --color-text: var(--text-primary);
  --color-text-secondary: var(--text-secondary);
  --color-text-muted: var(--text-muted);
  --color-text-placeholder: var(--text-placeholder);
  --color-text-disabled: var(--text-disabled);

  --color-accent: var(--accent-pink);
  --color-accent-hover: var(--accent-pink-hover);

  --color-success: var(--success);
  --color-warning: var(--warning);
  --color-error: var(--error);
  --color-info: var(--info);

  /* Typography */
  --font-sans: "Plus Jakarta Sans", "Noto Sans", system-ui, -apple-system, sans-serif;
  --font-mono: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;

  /* Spacing Scale */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;

  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-primary: 0 10px 15px -3px rgb(233 41 51 / 0.3), 0 4px 6px -4px rgb(233 41 51 / 0.3);

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

/* Global Styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background-primary);
  color: var(--text-primary);
  font-family: var(--font-sans);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-primary);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* Focus Styles */
:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* Selection Styles */
::selection {
  background: var(--primary);
  color: white;
}

/* Custom Utilities */
.video-thumbnail-overlay {
  position: absolute;
  bottom: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 0.25rem 0.5rem;
  margin: 0.5rem;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  line-height: 1rem;
}

.video-thumbnail-overlay-play {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  padding: 0.75rem;
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.group:hover .video-thumbnail-overlay-play {
  opacity: 1;
}

/* Category Card Animations */
.category-card {
  transition: transform var(--transition-fast);
}

.category-card:hover {
  transform: translateY(-4px);
}

.category-card:hover .category-image {
  filter: brightness(1.1);
}

.category-card:hover .category-title {
  color: var(--primary);
}

/* Form Input Focus States */
.form-input:focus {
  border-color: var(--accent-pink);
  box-shadow: 0 0 0 2px var(--accent-pink);
}

/* Line Clamp Utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Enhanced Video Card Animations */
.video-card-hover {
  transition: all var(--transition-fast);
}

.video-card-hover:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-primary);
}

/* Loading Animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Smooth Transitions */
.transition-smooth {
  transition: all var(--transition-normal);
}

/* Focus Ring */
.focus-ring {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}
