'use client';

import { useState, useMemo, useCallback } from 'react';
import { Video, SortOption } from '@/lib/types';
import { sortVideosByRecent, sortVideosByPopular } from '@/lib/mockData';

interface UseCategoryFilterProps {
  videos: Video[];
  initialSort?: SortOption;
}

interface UseCategoryFilterReturn {
  filteredVideos: Video[];
  activeSort: SortOption;
  setActiveSort: (sort: SortOption) => void;
  isLoading: boolean;
}

export function useCategoryFilter({ 
  videos, 
  initialSort = 'recent' 
}: UseCategoryFilterProps): UseCategoryFilterReturn {
  const [activeSort, setActiveSort] = useState<SortOption>(initialSort);
  const [isLoading, setIsLoading] = useState(false);

  // Memoized filtered and sorted videos
  const filteredVideos = useMemo(() => {
    if (!videos || videos.length === 0) return [];

    switch (activeSort) {
      case 'recent':
        return sortVideosByRecent(videos);
      case 'popular':
        return sortVideosByPopular(videos);
      case 'duration':
        return [...videos].sort((a, b) => b.duration - a.duration);
      case 'views':
        return [...videos].sort((a, b) => b.views - a.views);
      default:
        return videos;
    }
  }, [videos, activeSort]);

  // Handle sort change with loading state
  const handleSortChange = useCallback((sort: SortOption) => {
    if (sort === activeSort) return;
    
    setIsLoading(true);
    
    // Simulate API delay for smooth UX
    setTimeout(() => {
      setActiveSort(sort);
      setIsLoading(false);
    }, 300);
  }, [activeSort]);

  return {
    filteredVideos,
    activeSort,
    setActiveSort: handleSortChange,
    isLoading
  };
}
