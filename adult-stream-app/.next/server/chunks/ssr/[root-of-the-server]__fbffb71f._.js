module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/components/category/CategoryTitle.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CategoryTitle": (()=>CategoryTitle)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)");
'use client';
;
;
function CategoryTitle({ categoryName, videoCount, className }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `flex flex-wrap items-center justify-between gap-4 p-4 ${className || ''}`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex flex-col gap-2",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                        className: "flex items-center space-x-2 text-sm",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                href: "/",
                                className: "text-gray-400 hover:text-white transition-colors",
                                children: "Home"
                            }, void 0, false, {
                                fileName: "[project]/src/components/category/CategoryTitle.tsx",
                                lineNumber: 18,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-gray-600",
                                children: "/"
                            }, void 0, false, {
                                fileName: "[project]/src/components/category/CategoryTitle.tsx",
                                lineNumber: 24,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                href: "/categories",
                                className: "text-gray-400 hover:text-white transition-colors",
                                children: "Categories"
                            }, void 0, false, {
                                fileName: "[project]/src/components/category/CategoryTitle.tsx",
                                lineNumber: 25,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-gray-600",
                                children: "/"
                            }, void 0, false, {
                                fileName: "[project]/src/components/category/CategoryTitle.tsx",
                                lineNumber: 31,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-white font-medium",
                                children: categoryName
                            }, void 0, false, {
                                fileName: "[project]/src/components/category/CategoryTitle.tsx",
                                lineNumber: 32,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/category/CategoryTitle.tsx",
                        lineNumber: 17,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center gap-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                className: "text-white text-3xl font-bold leading-tight tracking-tight",
                                children: [
                                    "Category: ",
                                    categoryName
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/category/CategoryTitle.tsx",
                                lineNumber: 37,
                                columnNumber: 11
                            }, this),
                            videoCount !== undefined && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "bg-[#391D1F] text-gray-300 text-sm font-medium px-3 py-1 rounded-full",
                                children: [
                                    videoCount,
                                    " ",
                                    videoCount === 1 ? 'video' : 'videos'
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/category/CategoryTitle.tsx",
                                lineNumber: 41,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/category/CategoryTitle.tsx",
                        lineNumber: 36,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/category/CategoryTitle.tsx",
                lineNumber: 15,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center gap-2",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        className: "bg-[#E92933] hover:bg-[#d9202a] text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200",
                        children: "Follow Category"
                    }, void 0, false, {
                        fileName: "[project]/src/components/category/CategoryTitle.tsx",
                        lineNumber: 51,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        className: "text-gray-400 hover:text-white p-2 rounded-lg hover:bg-[#391D1F] transition-colors",
                        "aria-label": "More options",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                            className: "w-5 h-5",
                            fill: "currentColor",
                            viewBox: "0 0 24 24",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                d: "M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"
                            }, void 0, false, {
                                fileName: "[project]/src/components/category/CategoryTitle.tsx",
                                lineNumber: 61,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/category/CategoryTitle.tsx",
                            lineNumber: 60,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/category/CategoryTitle.tsx",
                        lineNumber: 56,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/category/CategoryTitle.tsx",
                lineNumber: 49,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/category/CategoryTitle.tsx",
        lineNumber: 14,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/components/category/CategoryTabs.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CategoryTabs": (()=>CategoryTabs)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
'use client';
;
function CategoryTabs({ activeTab, onTabChange, className }) {
    const tabs = [
        {
            key: 'recent',
            label: 'Recent'
        },
        {
            key: 'popular',
            label: 'Popular'
        }
    ];
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `pb-3 ${className || ''}`,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex border-b border-[#4A2528] px-4 gap-6 sm:gap-8",
            children: tabs.map((tab)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    onClick: ()=>onTabChange(tab.key),
                    className: `flex flex-col items-center justify-center border-b-[3px] pb-3 pt-4 transition-colors ${activeTab === tab.key ? 'border-[#E92933] text-white' : 'border-transparent text-[#C89295] hover:border-[#E92933]/50 hover:text-white'}`,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-sm font-semibold leading-normal tracking-[0.015em]",
                        children: tab.label
                    }, void 0, false, {
                        fileName: "[project]/src/components/category/CategoryTabs.tsx",
                        lineNumber: 31,
                        columnNumber: 13
                    }, this)
                }, tab.key, false, {
                    fileName: "[project]/src/components/category/CategoryTabs.tsx",
                    lineNumber: 22,
                    columnNumber: 11
                }, this))
        }, void 0, false, {
            fileName: "[project]/src/components/category/CategoryTabs.tsx",
            lineNumber: 20,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/category/CategoryTabs.tsx",
        lineNumber: 19,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/lib/utils.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "cn": (()=>cn),
    "debounce": (()=>debounce),
    "formatDuration": (()=>formatDuration),
    "formatTimeAgo": (()=>formatTimeAgo),
    "formatViews": (()=>formatViews),
    "throttle": (()=>throttle)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-ssr] (ecmascript)");
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
function formatDuration(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}
function formatViews(views) {
    if (views >= 1000000) {
        return `${(views / 1000000).toFixed(1)}M`;
    } else if (views >= 1000) {
        return `${(views / 1000).toFixed(1)}K`;
    }
    return views.toString();
}
function formatTimeAgo(date) {
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    if (diffInSeconds < 60) {
        return 'Just now';
    } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60);
        return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600);
        return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 604800) {
        const days = Math.floor(diffInSeconds / 86400);
        return `${days} day${days > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 2592000) {
        const weeks = Math.floor(diffInSeconds / 604800);
        return `${weeks} week${weeks > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 31536000) {
        const months = Math.floor(diffInSeconds / 2592000);
        return `${months} month${months > 1 ? 's' : ''} ago`;
    } else {
        const years = Math.floor(diffInSeconds / 31536000);
        return `${years} year${years > 1 ? 's' : ''} ago`;
    }
}
function debounce(func, wait) {
    let timeout;
    return (...args)=>{
        clearTimeout(timeout);
        timeout = setTimeout(()=>func(...args), wait);
    };
}
function throttle(func, limit) {
    let inThrottle;
    return (...args)=>{
        if (!inThrottle) {
            func(...args);
            inThrottle = true;
            setTimeout(()=>inThrottle = false, limit);
        }
    };
}
}}),
"[project]/src/components/category/CategoryVideoCard.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CategoryVideoCard": (()=>CategoryVideoCard)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
function CategoryVideoCard({ video, className, onClick }) {
    const handleClick = ()=>{
        if (onClick) {
            onClick();
        }
    };
    const cardContent = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `group relative aspect-video overflow-hidden rounded-lg shadow-lg transition-all hover:scale-105 ${className || ''}`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "relative w-full h-full",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        src: video.thumbnail,
                        alt: video.title,
                        fill: true,
                        className: "object-cover",
                        sizes: "(max-width: 640px) 50vw, (max-width: 768px) 33vw, (max-width: 1024px) 25vw, (max-width: 1280px) 20vw, 16vw",
                        priority: false
                    }, void 0, false, {
                        fileName: "[project]/src/components/category/CategoryVideoCard.tsx",
                        lineNumber: 28,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute inset-0 bg-gradient-to-t from-black/80 via-black/10 to-transparent"
                    }, void 0, false, {
                        fileName: "[project]/src/components/category/CategoryVideoCard.tsx",
                        lineNumber: 37,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/category/CategoryVideoCard.tsx",
                lineNumber: 27,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute inset-0 flex flex-col justify-end bg-gradient-to-t from-black/70 via-black/30 to-transparent p-3",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-sm font-bold text-white line-clamp-2 leading-tight",
                        children: video.title
                    }, void 0, false, {
                        fileName: "[project]/src/components/category/CategoryVideoCard.tsx",
                        lineNumber: 41,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "mt-1 text-xs text-gray-400",
                        children: [
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatViews"])(video.views),
                            " views • ",
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatTimeAgo"])(video.uploadedAt)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/category/CategoryVideoCard.tsx",
                        lineNumber: 44,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/category/CategoryVideoCard.tsx",
                lineNumber: 40,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute inset-0 flex items-center justify-center bg-black/50 opacity-0 transition-opacity group-hover:opacity-100",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                    className: "h-12 w-12 text-white",
                    fill: "currentColor",
                    viewBox: "0 0 256 256",
                    xmlns: "http://www.w3.org/2000/svg",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M240,128a15.71,15.71,0,0,1-7.6,13.51L88.32,223.25a16,16,0,0,1-24.32-13.51V46.26a16,16,0,0,1,24.32-13.51L232.4,114.49A15.71,15.71,0,0,1,240,128Z"
                    }, void 0, false, {
                        fileName: "[project]/src/components/category/CategoryVideoCard.tsx",
                        lineNumber: 52,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/category/CategoryVideoCard.tsx",
                    lineNumber: 51,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/category/CategoryVideoCard.tsx",
                lineNumber: 50,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute bottom-2 right-2 bg-black/70 text-white text-xs font-medium px-2 py-1 rounded",
                children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatDuration"])(video.duration)
            }, void 0, false, {
                fileName: "[project]/src/components/category/CategoryVideoCard.tsx",
                lineNumber: 57,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute top-2 left-2 flex gap-1",
                children: [
                    video.isHD && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "bg-[#E92933] text-white text-xs font-bold px-1.5 py-0.5 rounded",
                        children: "HD"
                    }, void 0, false, {
                        fileName: "[project]/src/components/category/CategoryVideoCard.tsx",
                        lineNumber: 64,
                        columnNumber: 11
                    }, this),
                    video.isPremium && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "bg-yellow-500 text-black text-xs font-bold px-1.5 py-0.5 rounded",
                        children: "PREMIUM"
                    }, void 0, false, {
                        fileName: "[project]/src/components/category/CategoryVideoCard.tsx",
                        lineNumber: 69,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/category/CategoryVideoCard.tsx",
                lineNumber: 62,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex gap-1",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            className: "bg-black/60 hover:bg-black/80 text-white p-1.5 rounded-full transition-colors",
                            onClick: (e)=>{
                                e.preventDefault();
                                e.stopPropagation();
                            // Handle favorite action
                            },
                            "aria-label": "Add to favorites",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                className: "w-4 h-4",
                                fill: "currentColor",
                                viewBox: "0 0 24 24",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                    d: "M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/category/CategoryVideoCard.tsx",
                                    lineNumber: 89,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/category/CategoryVideoCard.tsx",
                                lineNumber: 88,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/category/CategoryVideoCard.tsx",
                            lineNumber: 79,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            className: "bg-black/60 hover:bg-black/80 text-white p-1.5 rounded-full transition-colors",
                            onClick: (e)=>{
                                e.preventDefault();
                                e.stopPropagation();
                            // Handle watch later action
                            },
                            "aria-label": "Watch later",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                className: "w-4 h-4",
                                fill: "currentColor",
                                viewBox: "0 0 24 24",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                    d: "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/category/CategoryVideoCard.tsx",
                                    lineNumber: 104,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/category/CategoryVideoCard.tsx",
                                lineNumber: 103,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/category/CategoryVideoCard.tsx",
                            lineNumber: 94,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/category/CategoryVideoCard.tsx",
                    lineNumber: 77,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/category/CategoryVideoCard.tsx",
                lineNumber: 76,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/category/CategoryVideoCard.tsx",
        lineNumber: 23,
        columnNumber: 5
    }, this);
    if (onClick) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            onClick: handleClick,
            className: "cursor-pointer",
            children: cardContent
        }, void 0, false, {
            fileName: "[project]/src/components/category/CategoryVideoCard.tsx",
            lineNumber: 114,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
        href: `/video/${video.id}`,
        className: "block",
        children: cardContent
    }, void 0, false, {
        fileName: "[project]/src/components/category/CategoryVideoCard.tsx",
        lineNumber: 121,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/components/category/CategoryGrid.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CategoryGrid": (()=>CategoryGrid)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$category$2f$CategoryVideoCard$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/category/CategoryVideoCard.tsx [app-ssr] (ecmascript)");
'use client';
;
;
;
// Loading skeleton component
function VideoCardSkeleton() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "aspect-video bg-gray-700 rounded-lg animate-pulse",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "w-full h-full bg-gradient-to-t from-gray-800 to-gray-700 rounded-lg",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "absolute bottom-2 right-2 bg-gray-600 w-12 h-5 rounded animate-pulse"
                }, void 0, false, {
                    fileName: "[project]/src/components/category/CategoryGrid.tsx",
                    lineNumber: 21,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "absolute bottom-3 left-3 space-y-1",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "bg-gray-600 h-4 w-24 rounded animate-pulse"
                        }, void 0, false, {
                            fileName: "[project]/src/components/category/CategoryGrid.tsx",
                            lineNumber: 23,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "bg-gray-600 h-3 w-16 rounded animate-pulse"
                        }, void 0, false, {
                            fileName: "[project]/src/components/category/CategoryGrid.tsx",
                            lineNumber: 24,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/category/CategoryGrid.tsx",
                    lineNumber: 22,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/category/CategoryGrid.tsx",
            lineNumber: 19,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/category/CategoryGrid.tsx",
        lineNumber: 18,
        columnNumber: 5
    }, this);
}
// Empty state component
function EmptyState() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "col-span-full flex flex-col items-center justify-center py-12 text-center",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mb-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                    className: "w-16 h-16 text-gray-600 mx-auto",
                    fill: "none",
                    stroke: "currentColor",
                    viewBox: "0 0 24 24",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        strokeLinecap: "round",
                        strokeLinejoin: "round",
                        strokeWidth: 1.5,
                        d: "M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                    }, void 0, false, {
                        fileName: "[project]/src/components/category/CategoryGrid.tsx",
                        lineNumber: 37,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/category/CategoryGrid.tsx",
                    lineNumber: 36,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/category/CategoryGrid.tsx",
                lineNumber: 35,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                className: "text-lg font-semibold text-white mb-2",
                children: "No videos found"
            }, void 0, false, {
                fileName: "[project]/src/components/category/CategoryGrid.tsx",
                lineNumber: 40,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "text-gray-400 max-w-sm",
                children: "There are no videos in this category yet. Check back later for new content."
            }, void 0, false, {
                fileName: "[project]/src/components/category/CategoryGrid.tsx",
                lineNumber: 41,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/category/CategoryGrid.tsx",
        lineNumber: 34,
        columnNumber: 5
    }, this);
}
function CategoryGrid({ videos, loading = false, onLoadMore, hasMore = false, className }) {
    // Memoize video cards to prevent unnecessary re-renders
    const videoCards = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        return videos.map((video)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$category$2f$CategoryVideoCard$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CategoryVideoCard"], {
                video: video
            }, video.id, false, {
                fileName: "[project]/src/components/category/CategoryGrid.tsx",
                lineNumber: 58,
                columnNumber: 7
            }, this));
    }, [
        videos
    ]);
    // Show loading skeletons
    if (loading && videos.length === 0) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: `grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4 p-4 ${className || ''}`,
            children: Array.from({
                length: 12
            }).map((_, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(VideoCardSkeleton, {}, index, false, {
                    fileName: "[project]/src/components/category/CategoryGrid.tsx",
                    lineNumber: 69,
                    columnNumber: 11
                }, this))
        }, void 0, false, {
            fileName: "[project]/src/components/category/CategoryGrid.tsx",
            lineNumber: 67,
            columnNumber: 7
        }, this);
    }
    // Show empty state
    if (!loading && videos.length === 0) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: `grid grid-cols-1 p-4 ${className || ''}`,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(EmptyState, {}, void 0, false, {
                fileName: "[project]/src/components/category/CategoryGrid.tsx",
                lineNumber: 79,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/category/CategoryGrid.tsx",
            lineNumber: 78,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: className,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4 p-4",
                children: [
                    videoCards,
                    loading && videos.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: Array.from({
                            length: 6
                        }).map((_, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(VideoCardSkeleton, {}, `loading-${index}`, false, {
                                fileName: "[project]/src/components/category/CategoryGrid.tsx",
                                lineNumber: 94,
                                columnNumber: 15
                            }, this))
                    }, void 0, false)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/category/CategoryGrid.tsx",
                lineNumber: 87,
                columnNumber: 7
            }, this),
            !loading && hasMore && onLoadMore && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex justify-center p-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    onClick: onLoadMore,
                    className: "bg-[#E92933] hover:bg-[#d9202a] text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200",
                    children: "Load More Videos"
                }, void 0, false, {
                    fileName: "[project]/src/components/category/CategoryGrid.tsx",
                    lineNumber: 103,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/category/CategoryGrid.tsx",
                lineNumber: 102,
                columnNumber: 9
            }, this),
            !loading && !hasMore && videos.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center p-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-gray-400 text-sm",
                    children: "You've reached the end of this category"
                }, void 0, false, {
                    fileName: "[project]/src/components/category/CategoryGrid.tsx",
                    lineNumber: 115,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/category/CategoryGrid.tsx",
                lineNumber: 114,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/category/CategoryGrid.tsx",
        lineNumber: 85,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/lib/mockData.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "categories": (()=>categories),
    "getCategoryBySlug": (()=>getCategoryBySlug),
    "getVideosByCategory": (()=>getVideosByCategory),
    "isValidCategorySlug": (()=>isValidCategorySlug),
    "mockVideos": (()=>mockVideos),
    "sortVideosByPopular": (()=>sortVideosByPopular),
    "sortVideosByRecent": (()=>sortVideosByRecent)
});
// Helper function to create dates relative to now
const daysAgo = (days)=>{
    const date = new Date();
    date.setDate(date.getDate() - days);
    return date;
};
const mockVideos = [
    {
        id: '1',
        title: 'Young woman in lingerie',
        thumbnail: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDUy41muNMShuwIDWqjxpcQsV7KhLGyGjFY4zdqI7Lcv9QZT1oN4pWyVERS8-6L7o5y3C9GXEXy7FSJLA2KeZ0qQOZmBK9A4UgyD0CT61cpcZ5bKFbkydoy2pPARURUTf2WFHlzumJkGxLZ1R1FNrcqw-TQ9SmAaNA2EhR8CAppksKV8OmtYCDQ8J7erHloaYhJMPVYStNPL-K_buyV_qZz9utJBVUv7RUyV89suU8b4zyTDfRxaBopENbcMdAIcRAC5ytelvDvP2S4',
        duration: 754,
        views: 1200000,
        uploadedAt: daysAgo(2),
        category: 'Amateur',
        isHD: true,
        isPremium: false
    },
    {
        id: '2',
        title: 'Blonde woman in lingerie',
        thumbnail: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBUlCKajzjQ51XiZ1XJmkJn0Y9t1-BdUnT1khtxK_uCKPV0SaXL9jXdo6nhzOkWz1xxUIixENztti2MCFNZJ6t9ZeCmIWFbaD3CXQIEzxYYyeDDxtJQ7d-ilJ1dNPg-XKC_WzgP1wxSLTc2isQhvGvwj9UtqM4OgxYuH0cKyOfcRWt4IHEHFwF5B1JSoVp9Yna-CmWxsXeVBl8FBvJ_7htcaOOdzz2xL9WP5JHV0JvDA1-Mxezcf4BDrQhQDlKVlGTLgeWA8zRmztiF',
        duration: 876,
        views: 876000,
        uploadedAt: daysAgo(3),
        category: 'Blonde',
        isHD: true,
        isPremium: true
    },
    {
        id: '3',
        title: 'Brunette woman in lingerie',
        thumbnail: 'https://lh3.googleusercontent.com/aida-public/AB6AXuAEVaYijF6mrHoFDmO3Ou6JAyqc5srgGEsKgHh7BBycE9uDX8bLXJaET7CLsTUPtzOonp3DrbM4ktL_PgiNQC9rS6_XdUvblXWzZ9ibYzXNQWPdb9yzUqT7kOfKrAze_tOO5HwTsS2_FskSrZNOSJJdKr7mbq9TsdIABUv99KESPG2pMrckVGdcIQvzJ3zC9djcNW8IopWfiPic0IrnLhgBrD1aputtcv76dFdCGEcwIOIsKYFnRpYz9YKFGXQldrKk4lakhgFMv4kq',
        duration: 1260,
        views: 2100000,
        uploadedAt: daysAgo(1),
        category: 'Brunette',
        isHD: true,
        isPremium: false
    },
    {
        id: '4',
        title: 'Redhead woman in lingerie',
        thumbnail: 'https://lh3.googleusercontent.com/aida-public/AB6AXuA5UW7JBXy01u9nfj4vQ18qqtQPcUkusBi0l6R8YPHtm4Slp-wgwC7awH97TWOBVjhcmrhC1nVF3Y46nZ76B5mtnulzlXU4wxrOhMl7sYshljuBiFY1ZXbNbT-zQtJ62cd80iGwnnVvO7FtxjN072ZuUOBeqZhMwf4Wcmui29fNs9ikHX9Gh6kSixtcfzlQHk8OsnP0ofCgZMaMNqL8PqTVqGBB5Vdb6MkirIiDXrrCwmrzI4tc2_6p5y-Somi0JAdLr2CW-48pgZ8d',
        duration: 990,
        views: 990000,
        uploadedAt: daysAgo(5),
        category: 'Redhead',
        isHD: false,
        isPremium: false
    },
    {
        id: '5',
        title: 'Asian woman in lingerie',
        thumbnail: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDo0LKzHRIvjCB-31CLz0hnbWlCb3qVTKHgO7y3f1MsClO_3uI1JeykK-TSfVww2KfX9PbG7TCELgEf_cCdK1K9E5Z-Gs9z1AFz-toqoRMjwJaOqbyFntPUlQJXgwn1Yzs2YQWbauuSBi1hhqMmow08w1ujZ4I9inIcAi9rZ3NsDNaNCVFGLYVkDbxifUI-7OvHlzSNv8ew4YZZGE380MHB3kiYGFO8S6w2dZ9Y1n4L12xWeg7C8vij3Y630j5s4CdIYQvjYWCZxzq5',
        duration: 900,
        views: 1500000,
        uploadedAt: daysAgo(7),
        category: 'Asian',
        isHD: true,
        isPremium: true
    },
    {
        id: '6',
        title: 'Black woman in lingerie',
        thumbnail: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDC_SLMCvRT17RU2EgX8c86gxHNp0BsT7yDhIUycrt5ACfpynSejdT9qQm_1SikEwl9N0zgPG4WEjoX-PNZK1j72IoTr2fVZT7beEFUNQBQrLcrEeWzvzy5jqcDcQNBWCrpUeZiCGgIyWi-uL5G-UNC6JLBfH6MQTrlq0NVnBdsWBdEsgnPtmU7axWJsmsXlzKZNv3gLmPTD6JQ70OKwj8Sa7TGD8CYi9jnRjtO_Tr5WH6QiceQ1FweRMAY52UPHX0B4TG2zQqUlPVn',
        duration: 750,
        views: 750000,
        uploadedAt: daysAgo(4),
        category: 'Black',
        isHD: false,
        isPremium: false
    },
    // Additional videos for variety
    {
        id: '7',
        title: 'Passionate Encounter',
        thumbnail: 'https://lh3.googleusercontent.com/aida-public/AB6AXuAPcsend9trbNMM90GBRpbSNmC_Kq9aH0deWS779inHD442q5xdeNSeC0noqpamPqNOxZC3YGnD2yq8_90OxRMCzFJn8fwjiYvN1f4qF9f6d2d17s-UHIIyESBlqyKGcqIw7zZJldFxS_GUjIa0gyrmhEAdLVfRgvlGCjwzM2jRwVaXzVaMFXSdeHScL8JFYRse8qNZDKeJnvuFtrPSGyoYsLpzmArcK5rsca_MQnB0x0JwLB4IAb3H1QJV-h3yeZZIL04HW8tEybPA',
        duration: 754,
        views: 1800000,
        uploadedAt: daysAgo(1),
        category: 'MILF',
        isHD: true,
        isPremium: true
    },
    {
        id: '8',
        title: 'Late Night Rendezvous',
        thumbnail: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBtC4vkPEiw7ywgTvH2AZG1PJmLJxjG7YWL2OpKvTBNebCHj6hsiLlQTsxOXo6zQQyoW9RpYRbSW8I2RWe2zmD4SE_lt4RPDZU7rJXBhy3547GNtn31kvytwHnqJGlHW18ZZYEe0dKt09sRV__cqP1IQ2w2Xj5kksCpbM6ZDnoBNs_CCr06TvaPBP2-ZXqz5mubDGcjE-rc54viVN7FhjlYcvjIkzQyJhpXBHL4CpxvRrG1Wh8nSxsQeToSPKfaoHZowjpNhWmsELTK',
        duration: 922,
        views: 1200000,
        uploadedAt: daysAgo(2),
        category: 'POV',
        isHD: true,
        isPremium: false
    },
    {
        id: '9',
        title: 'Secret Affair',
        thumbnail: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBXKzntxMMeqjyHfY_PuIioiUYpbxHbL_cSciXRstabbro_M4wOCbiMcUr8eRgS8KQ8SQgqxjGo3gbFgCYdxY9KrLLd3YYblQSdgdXzxAHhNEmuyqUHAgefSqh0fmobRQ8eE8gGf8cBkZLMy1BjzCZtBZUOOsRA7u9EKeqodDmezQS3t-YZYHjV9KcMxjvmZa6NUBiGsp1MreGCkSije65LJUAZeAadk6uPg6Mza_y166XxolTQDhaZ6bp5pKG5rNhVN0uLfMfq4DQ1',
        duration: 611,
        views: 950000,
        uploadedAt: daysAgo(3),
        category: 'Teen',
        isHD: false,
        isPremium: false
    },
    {
        id: '10',
        title: 'Weekend Getaway',
        thumbnail: 'https://lh3.googleusercontent.com/aida-public/AB6AXuAfbpcWlrcdbP8DitSf39CiCeaYzWgIGEQYriU4dKdkC5Jm06Jhf7I1IDETuOeIS3FFmdT7tszIL6koCC1mToMOxTX2nEIwDa_cpxd0erFP-082DRWV3W7igswVXHehuoPf-Em8LywA1_BaFMyPRimH4LsJvx57EnBYLLLE2zOMJizmz6SSJjipPfaNv9j2hgA5N6jPcfxJKEde9g0JFQEa0iRaUtK77JXSQZowSvpRNNEg3MMA0HozTQYVJuBs4gM8qbxe-ihbjNh_',
        duration: 896,
        views: 1350000,
        uploadedAt: daysAgo(6),
        category: 'Mature',
        isHD: true,
        isPremium: true
    },
    {
        id: '11',
        title: 'Hidden Desires',
        thumbnail: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBnLslL3Vern129flANNuPYvJjXNqoyAHgNL3kS-39AtH4bZ7QZOgxnQ9toFMuNUccssz8igWkMYgsnua6HTWebkmNEWU7ghJqv5HCuiZnitNVp-nlXUgnqBBElhiHpcuPjcw3KDVc83lSfJQqof89V8xQLBgeHy4kHTtTqzKfBrSOsfiEUcjUus4ykdZICOjhkO77CBqeVeS4EvXREdPgJk3sEdo1_EWu92n96_XULAQPJLBXYoe6mY5GPxZHo-7-JWUKfaiX_ltRf',
        duration: 693,
        views: 780000,
        uploadedAt: daysAgo(8),
        category: 'Fetish',
        isHD: false,
        isPremium: false
    },
    {
        id: '12',
        title: 'Forbidden Love',
        thumbnail: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBFarMdSUyVCDAi34TczrqS_o6ZtyjovxzA7YmbbKhkdecb8zvQuLbkVjre5pYnDwL7j9T02M-AdbaIKNQuPyJoljxdWUnj2A2KdAcZXAndbREuEg-lhTumHsK8ZOcljyCSawduMSpdgZEiqI_MC5B-J1X_uWhmrXaOeH1qXJ3o6O-i8nLuy_Ttf5Fpx5lbWm6qIPjCwKz5m_Uc0SyziUCiGMREBGkN9hj70SNl_SMUEXVDpJgyTuqKxennvyF5dcTGESou1Bh7P7Zz',
        duration: 825,
        views: 1650000,
        uploadedAt: daysAgo(5),
        category: 'Lesbian',
        isHD: true,
        isPremium: true
    }
];
const categories = [
    {
        name: 'Big Tits',
        slug: 'big-tits',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDnVrvm1DwRW5Nfjjf3V27jy52hMYIGcco3ZlJtnPQO4XEmuPzX95VTiGpnpWNLgIDq-NBN8tfwwKs3GfJsNg9ykbfbVPEzy1sIbdTTFIQVLwM-Im9I40VWPNZMsjEqfLREM0ryz0zTxNKDEal-hBwuIoQtR73zy5F7Uu5gwwglcB9vZsoft22hhcTTCyliGOqmjenf6OFaiMEQyexn1cAsdeVyuO-u3Qjc5vyZKiOCq0tfDPB85WS36nkeiJ3H28nTJdyM82SEpV6x'
    },
    {
        name: 'MILF',
        slug: 'milf',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuCmjfZIDXtVPH_C-gs-SrE61OTbvUAGlwaJn6K3PV_CsY1hRwdMIKmdOZ4HKunOGhBtHrd-jH_mWiPJs-v0vmVH5WuiyoUiwNJ2QDTMYW6QXT19vVCPQX6A3l886Omv_CTKPtwIJe8Ajh7Hs_fxtXx5eVPRHQw7CdVbwHS4kjr5Fk28UGe7fCoZv4r01mnwsZJywEneQQIxww5VVyV6HP3ieQRGfCqwISJshl2fMdrrGxqpB3y1DUz448HIu5YNUryW8Zb9dWqydG5J'
    },
    {
        name: 'POV',
        slug: 'pov',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDtxp-r8-nct5ErDgiC3Y9K_7Lfh3wC4iFWqvaendssdIIdjbOY2I2oRMZp8Iq20v4zadIclbMQmoFWiHAM6VI9fZWUScgCbPWVVfOKEJc-OH33-q0RRYj2iHbttz5-rWCQ-4x4ZrNv4MS_QixUIAEMICXz5Q7dcBrq4OtyUiD9dCybkNsFKlK_jDKw2aeA0DbXSK2uT6QKm9FdyrWKJjfh-qoAr5DaezpTaVSuSPmbJ3we5FldeCnwRvmZpzFjBa8yvpe_xvjaZ3kY'
    },
    {
        name: 'Blonde',
        slug: 'blonde',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBWhSOS9vn6_9_6ejf1HFOULifKtjb-NFozoa7WleoS9BRTEpXTKgt3W9Ea1BQLTFjGaHHM_Rp0BT3RApvbKTBLWeJ0PQ2_D-8V3oKKiALVQvRjrjztPVbtLkIxx5E-RHhvftLVi2uWO0aO-auPqWFXQnf4DsjWToNQ_dnekwCbssmkuOi2vEW_FaWPSsIn-tIbvkKXCerIfqvXxMFSXuGbqIAS89EEgFiYgg96k0s1TEmm6TTqbRa0KCGzQ5xTqZUfjMMHsv0OKKnt'
    },
    {
        name: 'Brunette',
        slug: 'brunette',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDYi3vd_czZqaYivg094RWWAgCePpYzid85bqtivFxlQM8XBRFQeQkKHnC5fG-TkUjcW-n1oru_RVUyF2PMPMJ7fBP9SFXvM0dW2KY-Ub4ziOQncBtktrWQh_wmQrXxzZSCFGVCBa0WzlRtoKjgzW_AMEL48WkPvqfrjv0PG3_pQC8apwhp9H-TMDU2PTVANYxrzgaVe39zwICHYlVd7dgav9TQTGjs8X9AmOcRhInTShad2rpa3ysDQjhf3gnIp5-7BdjKNb1VgCcT'
    },
    {
        name: 'Redhead',
        slug: 'redhead',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuAwYqIvpZMNreOs2rxl7FDpHTo1jHTtaWMZ9xrhVQc7XEFsU_jldHjP7NjD682DYCKRM_zGH6PaLnHcjsS9GW-LOozqc6LU5cpu4eCU_6QtxdIaKSUyvK2n7jHjSVQhhEa4sFrf3-5dpcRUyYR-ZREoFvfFn2AqJZd4OzHckRVBQzzn5q6LdVAIz7Npb3CInyMgVAPcNN8e8QOruXo6m4qhCiivXfcjKxB7Ow0Z9qNfhsdXuvO62IBIzlD3-pL-0DLFmUb-Y9ZlDZrR'
    },
    {
        name: 'Asian',
        slug: 'asian',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBkLz9rJ6E4jNXu9ooPNepwPnZO_N5GzQ8mNZIGCPw_0ejGE4i1_AhOIQ9sLZT86No-qx8DN4EGXk8DGvjZJwhfIK0FanNu-UZ0MlOCPwbzJ-rme_WK5ombatxOXm9J9j2wYfMTAvp8AmehaSHige6iCyyFyb9JqjgW6P_BKWjnXePo80jhsTUW6ZxFcAnxug6CTr4HmPE824Aje2kF9fRyKjG2jZWyj0MPCKMx4x-pqg5YCZ89Mne66TGMwGGNe9YVsNg1hAEL_0h9'
    },
    {
        name: 'Black',
        slug: 'black',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuAKNscCiRR2AU5xuk7Sto_4jCpAWlAs8WVww2duHrGIZDciz9Vq6eUJ1T5xgUbzQ9ONv57gcI1LEIfjWCT-nQGxqTxkcdL3P6cQl6hYOmbEhEOimlLCaPLckHM2jnuvbVUjSuZbGUs15PUyefjz7t-bHnojwYp0TPDXah0FZwHWe0nhsmo2kEiC9fxmhzya3bQYRsLHMWw9v7Ugt4wCzdQXiclLdEbTEZ2s_odiZe-vj3pDG9MJ1gDCZ1l5ajMb3BN1c3L8ODsSLmqc'
    },
    {
        name: 'Latina',
        slug: 'latina',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuA7gY2rMtr_fSF1n2aeF1jWyPwpCas0JbNAeeZPrSQOcHxyZEjMs_U1yzSzHBzBMBq2vN4KKdcg2kujejDR2UAshHiTSdSiNJNB95FckaFVaU2wcH6BLqAG4u04KtAZY9R8ovvaHXftcVzxklRp8x2JgEJLvx37C6hIPZX0YZyg00sHsuOx65eOhHb9vz2XzeczA2oHR2lPc9yBTAfgxYF6JCBFLFdHgd-U6lbgugoL53Y85orncyjUvqRShrMRUnDwObdAH9lVrfNd'
    },
    {
        name: 'Amateur',
        slug: 'amateur',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBf2N2pLIYjyWHFQ6HGO97OAKvHNuktfcICn-4o6q2CRfU4D16K5tuwbrM7ySgZBMKnXlGWGOLGO0W4qLv-uNRhGKsdHvsvRQxBrACW4E152Lj3Dh-vykrTDm9Y7DtNwHptp3TyRQwqBKaZMSP4FmT99T2l1ay0SoQE24LT5Y85p3kpiF-o4-zQkHKoWFjQLAQctxRKgkJJcvhgUdR_pC3NEhRWwmHN_ogRpQA7tP5YagV0jr3hOE9XbAqaCPfZrTog0ihGFhfWRAba'
    },
    {
        name: 'Teen',
        slug: 'teen',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuA1DgnjUG2m78BS31Hh_p0_aEiNnUdgYeo3bAJvnDuadnvu1Mn-M7m9C84SIzavcd6jMIlC87tuCUvjAYE6s3kssrgx3vVWJafX1bzWjktOK9w4-dZ4fFuiELj7DzntR2T6L6AY3ak58qf5sTE0bRDUuS0ImhVGUAdYAT4JmImmwG8i9v7DCFj3_BtTneJdOHtGIDGt9M32j4L8vz65GXs1FAT6QG7Dem70WL-vPzr_GozEIW7H1dVr1shGYfJf5fHWCxO52znTuqk8'
    },
    {
        name: 'Mature',
        slug: 'mature',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuB399n5jsMLZP3E521_95YGyUj-JYCbAJRoMTXx8YoEhJ47CYdy-td9lfDhgXJjosQbXvV9QDYTroRoSP9m_vV4ipPQIUyRVcK8gsN4nF7KibY6SnI9Qc7AxKL3hXpe_o505GQAjhX0wOo1bOqwx3k4BUtuFervKeoA-VWP-h_JW2jASYcbsQrGVTvZbqr4WgqerKhHvtbbDUrunP_PvHheg9m1bxvp77iCQlk2iWLruLxfpdS0ymglBjANZfdtU4kPS8TXqKP39K3B'
    },
    {
        name: 'Fetish',
        slug: 'fetish',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBf-LU36CTb5r5ts_sQCH41lTXcfGFz1ml_iNqKQQBoD0BtBb9QzRH8lTMFBiZdbZM9xjchoTVx24hBy0KVfPqj5lIvHCk_Qd4-DDj-W6s3SnbwXXaM7BohcTIElYOb9VZjBtsE3PX7Sok1ha7Ni-k8iLrR1kzragF5DwKmmopPgsY6NoODvRe4oTYd_7b6Xt0vxuxPMclfwZ0Lem-4Ky2UAykKc6FyAjRrSJPfuj5ADZcXPY_-3momBEh8N9chZ56kFoI7SYKcY69V'
    },
    {
        name: 'Gay',
        slug: 'gay',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuAxwTW-zXUzUx_Usv5ZRAgn6gW6ZcJsbbXR8Id4piLm4wEy3F6eilSkZnvQp2ImmqDGzxm9mtUjnION_jxCuXsNsaGCPxLptAdJ8H6guWmzhpvnhPbSf-rRlriQE5np_bqdW3BWwzm6zyKd0UUTWMYzSusBsPRfAUqojCD-AD2vVUMzYHVCC3tJannfVnSvEg65Co94-2zMysoXEPE_ZB88-9wkH5jmVcOpyRsT27V0GdcbTd6nYHlx3BXWmnylM2O0BzX-iU6iruSi'
    },
    {
        name: 'Lesbian',
        slug: 'lesbian',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuA7xQu0FWYiw_vrF5lwSn5UgxhSRYcJyZWisEsOqVlUPPmGd9oaMeS-S3_zq7nit3La7ZrZn4bDf6v_8K2IVfmB9lwVs7UQM7salTbgRHRO-eO9DKbeulQTlw7xa0y_M_gN7RRIpuwO81xAa3VP6ijxcbWASQM4uR6TxUi8BMqjIsi1Oi12hv9JI0wGeuPjSLyJXzs4zHIQQTW3dF0DrPYI29qJG6gkOwnAjtEXpFYe2FO5y4rqfKg3EhXSMSycfbTk8-NbHNgC7eHe'
    },
    {
        name: 'Transgender',
        slug: 'transgender',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuA8YwPHRUZNPcf1Ab6FrvfXWLLoiTeLzD-GpIsP8PPCz0dXO6G6NSXWSxox11bzLt-gwDPJieIeUFv0ZgkfPrRf5Yc-8KtUUFdzr3P1W998eWxSpptQ3XuSPYJEh9eEmOOST_NZyEJRsDy45fYmwwpiYwkjjSxr19tsOIZiON5_nPU4dbSAMKchh6EJl1NXMZ7QjSkckQSVje5Gp-Ve1C0IUonXe6-e0tMX63W7BPEypLAQJWRl_PyQfs7saMhy_27BXnrhl40k1PQ4'
    }
];
function getCategoryBySlug(slug) {
    return categories.find((cat)=>cat.slug === slug);
}
function getVideosByCategory(categorySlug) {
    const category = getCategoryBySlug(categorySlug);
    if (!category) return [];
    // For now, return all videos. In a real app, this would filter by category
    return mockVideos;
}
function sortVideosByRecent(videos) {
    return [
        ...videos
    ].sort((a, b)=>b.uploadedAt.getTime() - a.uploadedAt.getTime());
}
function sortVideosByPopular(videos) {
    return [
        ...videos
    ].sort((a, b)=>b.views - a.views);
}
function isValidCategorySlug(slug) {
    return categories.some((cat)=>cat.slug === slug);
}
}}),
"[project]/src/hooks/useCategoryFilter.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useCategoryFilter": (()=>useCategoryFilter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockData$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mockData.ts [app-ssr] (ecmascript)");
'use client';
;
;
function useCategoryFilter({ videos, initialSort = 'recent' }) {
    const [activeSort, setActiveSort] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(initialSort);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Memoized filtered and sorted videos
    const filteredVideos = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        if (!videos || videos.length === 0) return [];
        switch(activeSort){
            case 'recent':
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockData$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["sortVideosByRecent"])(videos);
            case 'popular':
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockData$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["sortVideosByPopular"])(videos);
            case 'duration':
                return [
                    ...videos
                ].sort((a, b)=>b.duration - a.duration);
            case 'views':
                return [
                    ...videos
                ].sort((a, b)=>b.views - a.views);
            default:
                return videos;
        }
    }, [
        videos,
        activeSort
    ]);
    // Handle sort change with loading state
    const handleSortChange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((sort)=>{
        if (sort === activeSort) return;
        setIsLoading(true);
        // Simulate API delay for smooth UX
        setTimeout(()=>{
            setActiveSort(sort);
            setIsLoading(false);
        }, 300);
    }, [
        activeSort
    ]);
    return {
        filteredVideos,
        activeSort,
        setActiveSort: handleSortChange,
        isLoading
    };
}
}}),
"[project]/src/components/category/CategoryPageContent.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CategoryPageContent": (()=>CategoryPageContent)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$category$2f$CategoryTitle$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/category/CategoryTitle.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$category$2f$CategoryTabs$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/category/CategoryTabs.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$category$2f$CategoryGrid$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/category/CategoryGrid.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useCategoryFilter$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useCategoryFilter.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
function CategoryPageContent({ category, videos }) {
    // Use filtering hook
    const { filteredVideos, activeSort, setActiveSort, isLoading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useCategoryFilter$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCategoryFilter"])({
        videos,
        initialSort: 'recent'
    });
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$category$2f$CategoryTitle$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CategoryTitle"], {
                categoryName: category.name,
                videoCount: filteredVideos.length
            }, void 0, false, {
                fileName: "[project]/src/components/category/CategoryPageContent.tsx",
                lineNumber: 25,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$category$2f$CategoryTabs$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CategoryTabs"], {
                activeTab: activeSort,
                onTabChange: setActiveSort
            }, void 0, false, {
                fileName: "[project]/src/components/category/CategoryPageContent.tsx",
                lineNumber: 31,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$category$2f$CategoryGrid$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CategoryGrid"], {
                videos: filteredVideos,
                loading: isLoading,
                hasMore: false
            }, void 0, false, {
                fileName: "[project]/src/components/category/CategoryPageContent.tsx",
                lineNumber: 37,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__fbffb71f._.js.map