module.exports = {

"[project]/.next-internal/server/app/categories/[category]/page/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[project]/src/app/favicon.ico.mjs { IMAGE => \"[project]/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/favicon.ico.mjs { IMAGE => \"[project]/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}}),
"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/src/app/categories/[category]/loading.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/categories/[category]/loading.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/src/components/category/CategoryLayout.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CategoryLayout": (()=>CategoryLayout)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const CategoryLayout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call CategoryLayout() from the server but CategoryLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/category/CategoryLayout.tsx <module evaluation>", "CategoryLayout");
}}),
"[project]/src/components/category/CategoryLayout.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CategoryLayout": (()=>CategoryLayout)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const CategoryLayout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call CategoryLayout() from the server but CategoryLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/category/CategoryLayout.tsx", "CategoryLayout");
}}),
"[project]/src/components/category/CategoryLayout.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$category$2f$CategoryLayout$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/category/CategoryLayout.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$category$2f$CategoryLayout$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/src/components/category/CategoryLayout.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$category$2f$CategoryLayout$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/src/components/category/CategoryPageContent.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CategoryPageContent": (()=>CategoryPageContent)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const CategoryPageContent = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call CategoryPageContent() from the server but CategoryPageContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/category/CategoryPageContent.tsx <module evaluation>", "CategoryPageContent");
}}),
"[project]/src/components/category/CategoryPageContent.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CategoryPageContent": (()=>CategoryPageContent)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const CategoryPageContent = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call CategoryPageContent() from the server but CategoryPageContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/category/CategoryPageContent.tsx", "CategoryPageContent");
}}),
"[project]/src/components/category/CategoryPageContent.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$category$2f$CategoryPageContent$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/category/CategoryPageContent.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$category$2f$CategoryPageContent$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/src/components/category/CategoryPageContent.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$category$2f$CategoryPageContent$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/src/lib/mockData.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "categories": (()=>categories),
    "getCategoryBySlug": (()=>getCategoryBySlug),
    "getVideosByCategory": (()=>getVideosByCategory),
    "isValidCategorySlug": (()=>isValidCategorySlug),
    "mockVideos": (()=>mockVideos),
    "sortVideosByPopular": (()=>sortVideosByPopular),
    "sortVideosByRecent": (()=>sortVideosByRecent)
});
// Helper function to create dates relative to now
const daysAgo = (days)=>{
    const date = new Date();
    date.setDate(date.getDate() - days);
    return date;
};
const mockVideos = [
    {
        id: '1',
        title: 'Young woman in lingerie',
        thumbnail: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDUy41muNMShuwIDWqjxpcQsV7KhLGyGjFY4zdqI7Lcv9QZT1oN4pWyVERS8-6L7o5y3C9GXEXy7FSJLA2KeZ0qQOZmBK9A4UgyD0CT61cpcZ5bKFbkydoy2pPARURUTf2WFHlzumJkGxLZ1R1FNrcqw-TQ9SmAaNA2EhR8CAppksKV8OmtYCDQ8J7erHloaYhJMPVYStNPL-K_buyV_qZz9utJBVUv7RUyV89suU8b4zyTDfRxaBopENbcMdAIcRAC5ytelvDvP2S4',
        duration: 754,
        views: 1200000,
        uploadedAt: daysAgo(2),
        category: 'Amateur',
        isHD: true,
        isPremium: false
    },
    {
        id: '2',
        title: 'Blonde woman in lingerie',
        thumbnail: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBUlCKajzjQ51XiZ1XJmkJn0Y9t1-BdUnT1khtxK_uCKPV0SaXL9jXdo6nhzOkWz1xxUIixENztti2MCFNZJ6t9ZeCmIWFbaD3CXQIEzxYYyeDDxtJQ7d-ilJ1dNPg-XKC_WzgP1wxSLTc2isQhvGvwj9UtqM4OgxYuH0cKyOfcRWt4IHEHFwF5B1JSoVp9Yna-CmWxsXeVBl8FBvJ_7htcaOOdzz2xL9WP5JHV0JvDA1-Mxezcf4BDrQhQDlKVlGTLgeWA8zRmztiF',
        duration: 876,
        views: 876000,
        uploadedAt: daysAgo(3),
        category: 'Blonde',
        isHD: true,
        isPremium: true
    },
    {
        id: '3',
        title: 'Brunette woman in lingerie',
        thumbnail: 'https://lh3.googleusercontent.com/aida-public/AB6AXuAEVaYijF6mrHoFDmO3Ou6JAyqc5srgGEsKgHh7BBycE9uDX8bLXJaET7CLsTUPtzOonp3DrbM4ktL_PgiNQC9rS6_XdUvblXWzZ9ibYzXNQWPdb9yzUqT7kOfKrAze_tOO5HwTsS2_FskSrZNOSJJdKr7mbq9TsdIABUv99KESPG2pMrckVGdcIQvzJ3zC9djcNW8IopWfiPic0IrnLhgBrD1aputtcv76dFdCGEcwIOIsKYFnRpYz9YKFGXQldrKk4lakhgFMv4kq',
        duration: 1260,
        views: 2100000,
        uploadedAt: daysAgo(1),
        category: 'Brunette',
        isHD: true,
        isPremium: false
    },
    {
        id: '4',
        title: 'Redhead woman in lingerie',
        thumbnail: 'https://lh3.googleusercontent.com/aida-public/AB6AXuA5UW7JBXy01u9nfj4vQ18qqtQPcUkusBi0l6R8YPHtm4Slp-wgwC7awH97TWOBVjhcmrhC1nVF3Y46nZ76B5mtnulzlXU4wxrOhMl7sYshljuBiFY1ZXbNbT-zQtJ62cd80iGwnnVvO7FtxjN072ZuUOBeqZhMwf4Wcmui29fNs9ikHX9Gh6kSixtcfzlQHk8OsnP0ofCgZMaMNqL8PqTVqGBB5Vdb6MkirIiDXrrCwmrzI4tc2_6p5y-Somi0JAdLr2CW-48pgZ8d',
        duration: 990,
        views: 990000,
        uploadedAt: daysAgo(5),
        category: 'Redhead',
        isHD: false,
        isPremium: false
    },
    {
        id: '5',
        title: 'Asian woman in lingerie',
        thumbnail: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDo0LKzHRIvjCB-31CLz0hnbWlCb3qVTKHgO7y3f1MsClO_3uI1JeykK-TSfVww2KfX9PbG7TCELgEf_cCdK1K9E5Z-Gs9z1AFz-toqoRMjwJaOqbyFntPUlQJXgwn1Yzs2YQWbauuSBi1hhqMmow08w1ujZ4I9inIcAi9rZ3NsDNaNCVFGLYVkDbxifUI-7OvHlzSNv8ew4YZZGE380MHB3kiYGFO8S6w2dZ9Y1n4L12xWeg7C8vij3Y630j5s4CdIYQvjYWCZxzq5',
        duration: 900,
        views: 1500000,
        uploadedAt: daysAgo(7),
        category: 'Asian',
        isHD: true,
        isPremium: true
    },
    {
        id: '6',
        title: 'Black woman in lingerie',
        thumbnail: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDC_SLMCvRT17RU2EgX8c86gxHNp0BsT7yDhIUycrt5ACfpynSejdT9qQm_1SikEwl9N0zgPG4WEjoX-PNZK1j72IoTr2fVZT7beEFUNQBQrLcrEeWzvzy5jqcDcQNBWCrpUeZiCGgIyWi-uL5G-UNC6JLBfH6MQTrlq0NVnBdsWBdEsgnPtmU7axWJsmsXlzKZNv3gLmPTD6JQ70OKwj8Sa7TGD8CYi9jnRjtO_Tr5WH6QiceQ1FweRMAY52UPHX0B4TG2zQqUlPVn',
        duration: 750,
        views: 750000,
        uploadedAt: daysAgo(4),
        category: 'Black',
        isHD: false,
        isPremium: false
    },
    // Additional videos for variety
    {
        id: '7',
        title: 'Passionate Encounter',
        thumbnail: 'https://lh3.googleusercontent.com/aida-public/AB6AXuAPcsend9trbNMM90GBRpbSNmC_Kq9aH0deWS779inHD442q5xdeNSeC0noqpamPqNOxZC3YGnD2yq8_90OxRMCzFJn8fwjiYvN1f4qF9f6d2d17s-UHIIyESBlqyKGcqIw7zZJldFxS_GUjIa0gyrmhEAdLVfRgvlGCjwzM2jRwVaXzVaMFXSdeHScL8JFYRse8qNZDKeJnvuFtrPSGyoYsLpzmArcK5rsca_MQnB0x0JwLB4IAb3H1QJV-h3yeZZIL04HW8tEybPA',
        duration: 754,
        views: 1800000,
        uploadedAt: daysAgo(1),
        category: 'MILF',
        isHD: true,
        isPremium: true
    },
    {
        id: '8',
        title: 'Late Night Rendezvous',
        thumbnail: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBtC4vkPEiw7ywgTvH2AZG1PJmLJxjG7YWL2OpKvTBNebCHj6hsiLlQTsxOXo6zQQyoW9RpYRbSW8I2RWe2zmD4SE_lt4RPDZU7rJXBhy3547GNtn31kvytwHnqJGlHW18ZZYEe0dKt09sRV__cqP1IQ2w2Xj5kksCpbM6ZDnoBNs_CCr06TvaPBP2-ZXqz5mubDGcjE-rc54viVN7FhjlYcvjIkzQyJhpXBHL4CpxvRrG1Wh8nSxsQeToSPKfaoHZowjpNhWmsELTK',
        duration: 922,
        views: 1200000,
        uploadedAt: daysAgo(2),
        category: 'POV',
        isHD: true,
        isPremium: false
    },
    {
        id: '9',
        title: 'Secret Affair',
        thumbnail: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBXKzntxMMeqjyHfY_PuIioiUYpbxHbL_cSciXRstabbro_M4wOCbiMcUr8eRgS8KQ8SQgqxjGo3gbFgCYdxY9KrLLd3YYblQSdgdXzxAHhNEmuyqUHAgefSqh0fmobRQ8eE8gGf8cBkZLMy1BjzCZtBZUOOsRA7u9EKeqodDmezQS3t-YZYHjV9KcMxjvmZa6NUBiGsp1MreGCkSije65LJUAZeAadk6uPg6Mza_y166XxolTQDhaZ6bp5pKG5rNhVN0uLfMfq4DQ1',
        duration: 611,
        views: 950000,
        uploadedAt: daysAgo(3),
        category: 'Teen',
        isHD: false,
        isPremium: false
    },
    {
        id: '10',
        title: 'Weekend Getaway',
        thumbnail: 'https://lh3.googleusercontent.com/aida-public/AB6AXuAfbpcWlrcdbP8DitSf39CiCeaYzWgIGEQYriU4dKdkC5Jm06Jhf7I1IDETuOeIS3FFmdT7tszIL6koCC1mToMOxTX2nEIwDa_cpxd0erFP-082DRWV3W7igswVXHehuoPf-Em8LywA1_BaFMyPRimH4LsJvx57EnBYLLLE2zOMJizmz6SSJjipPfaNv9j2hgA5N6jPcfxJKEde9g0JFQEa0iRaUtK77JXSQZowSvpRNNEg3MMA0HozTQYVJuBs4gM8qbxe-ihbjNh_',
        duration: 896,
        views: 1350000,
        uploadedAt: daysAgo(6),
        category: 'Mature',
        isHD: true,
        isPremium: true
    },
    {
        id: '11',
        title: 'Hidden Desires',
        thumbnail: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBnLslL3Vern129flANNuPYvJjXNqoyAHgNL3kS-39AtH4bZ7QZOgxnQ9toFMuNUccssz8igWkMYgsnua6HTWebkmNEWU7ghJqv5HCuiZnitNVp-nlXUgnqBBElhiHpcuPjcw3KDVc83lSfJQqof89V8xQLBgeHy4kHTtTqzKfBrSOsfiEUcjUus4ykdZICOjhkO77CBqeVeS4EvXREdPgJk3sEdo1_EWu92n96_XULAQPJLBXYoe6mY5GPxZHo-7-JWUKfaiX_ltRf',
        duration: 693,
        views: 780000,
        uploadedAt: daysAgo(8),
        category: 'Fetish',
        isHD: false,
        isPremium: false
    },
    {
        id: '12',
        title: 'Forbidden Love',
        thumbnail: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBFarMdSUyVCDAi34TczrqS_o6ZtyjovxzA7YmbbKhkdecb8zvQuLbkVjre5pYnDwL7j9T02M-AdbaIKNQuPyJoljxdWUnj2A2KdAcZXAndbREuEg-lhTumHsK8ZOcljyCSawduMSpdgZEiqI_MC5B-J1X_uWhmrXaOeH1qXJ3o6O-i8nLuy_Ttf5Fpx5lbWm6qIPjCwKz5m_Uc0SyziUCiGMREBGkN9hj70SNl_SMUEXVDpJgyTuqKxennvyF5dcTGESou1Bh7P7Zz',
        duration: 825,
        views: 1650000,
        uploadedAt: daysAgo(5),
        category: 'Lesbian',
        isHD: true,
        isPremium: true
    }
];
const categories = [
    {
        name: 'Big Tits',
        slug: 'big-tits',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDnVrvm1DwRW5Nfjjf3V27jy52hMYIGcco3ZlJtnPQO4XEmuPzX95VTiGpnpWNLgIDq-NBN8tfwwKs3GfJsNg9ykbfbVPEzy1sIbdTTFIQVLwM-Im9I40VWPNZMsjEqfLREM0ryz0zTxNKDEal-hBwuIoQtR73zy5F7Uu5gwwglcB9vZsoft22hhcTTCyliGOqmjenf6OFaiMEQyexn1cAsdeVyuO-u3Qjc5vyZKiOCq0tfDPB85WS36nkeiJ3H28nTJdyM82SEpV6x'
    },
    {
        name: 'MILF',
        slug: 'milf',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuCmjfZIDXtVPH_C-gs-SrE61OTbvUAGlwaJn6K3PV_CsY1hRwdMIKmdOZ4HKunOGhBtHrd-jH_mWiPJs-v0vmVH5WuiyoUiwNJ2QDTMYW6QXT19vVCPQX6A3l886Omv_CTKPtwIJe8Ajh7Hs_fxtXx5eVPRHQw7CdVbwHS4kjr5Fk28UGe7fCoZv4r01mnwsZJywEneQQIxww5VVyV6HP3ieQRGfCqwISJshl2fMdrrGxqpB3y1DUz448HIu5YNUryW8Zb9dWqydG5J'
    },
    {
        name: 'POV',
        slug: 'pov',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDtxp-r8-nct5ErDgiC3Y9K_7Lfh3wC4iFWqvaendssdIIdjbOY2I2oRMZp8Iq20v4zadIclbMQmoFWiHAM6VI9fZWUScgCbPWVVfOKEJc-OH33-q0RRYj2iHbttz5-rWCQ-4x4ZrNv4MS_QixUIAEMICXz5Q7dcBrq4OtyUiD9dCybkNsFKlK_jDKw2aeA0DbXSK2uT6QKm9FdyrWKJjfh-qoAr5DaezpTaVSuSPmbJ3we5FldeCnwRvmZpzFjBa8yvpe_xvjaZ3kY'
    },
    {
        name: 'Blonde',
        slug: 'blonde',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBWhSOS9vn6_9_6ejf1HFOULifKtjb-NFozoa7WleoS9BRTEpXTKgt3W9Ea1BQLTFjGaHHM_Rp0BT3RApvbKTBLWeJ0PQ2_D-8V3oKKiALVQvRjrjztPVbtLkIxx5E-RHhvftLVi2uWO0aO-auPqWFXQnf4DsjWToNQ_dnekwCbssmkuOi2vEW_FaWPSsIn-tIbvkKXCerIfqvXxMFSXuGbqIAS89EEgFiYgg96k0s1TEmm6TTqbRa0KCGzQ5xTqZUfjMMHsv0OKKnt'
    },
    {
        name: 'Brunette',
        slug: 'brunette',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDYi3vd_czZqaYivg094RWWAgCePpYzid85bqtivFxlQM8XBRFQeQkKHnC5fG-TkUjcW-n1oru_RVUyF2PMPMJ7fBP9SFXvM0dW2KY-Ub4ziOQncBtktrWQh_wmQrXxzZSCFGVCBa0WzlRtoKjgzW_AMEL48WkPvqfrjv0PG3_pQC8apwhp9H-TMDU2PTVANYxrzgaVe39zwICHYlVd7dgav9TQTGjs8X9AmOcRhInTShad2rpa3ysDQjhf3gnIp5-7BdjKNb1VgCcT'
    },
    {
        name: 'Redhead',
        slug: 'redhead',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuAwYqIvpZMNreOs2rxl7FDpHTo1jHTtaWMZ9xrhVQc7XEFsU_jldHjP7NjD682DYCKRM_zGH6PaLnHcjsS9GW-LOozqc6LU5cpu4eCU_6QtxdIaKSUyvK2n7jHjSVQhhEa4sFrf3-5dpcRUyYR-ZREoFvfFn2AqJZd4OzHckRVBQzzn5q6LdVAIz7Npb3CInyMgVAPcNN8e8QOruXo6m4qhCiivXfcjKxB7Ow0Z9qNfhsdXuvO62IBIzlD3-pL-0DLFmUb-Y9ZlDZrR'
    },
    {
        name: 'Asian',
        slug: 'asian',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBkLz9rJ6E4jNXu9ooPNepwPnZO_N5GzQ8mNZIGCPw_0ejGE4i1_AhOIQ9sLZT86No-qx8DN4EGXk8DGvjZJwhfIK0FanNu-UZ0MlOCPwbzJ-rme_WK5ombatxOXm9J9j2wYfMTAvp8AmehaSHige6iCyyFyb9JqjgW6P_BKWjnXePo80jhsTUW6ZxFcAnxug6CTr4HmPE824Aje2kF9fRyKjG2jZWyj0MPCKMx4x-pqg5YCZ89Mne66TGMwGGNe9YVsNg1hAEL_0h9'
    },
    {
        name: 'Black',
        slug: 'black',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuAKNscCiRR2AU5xuk7Sto_4jCpAWlAs8WVww2duHrGIZDciz9Vq6eUJ1T5xgUbzQ9ONv57gcI1LEIfjWCT-nQGxqTxkcdL3P6cQl6hYOmbEhEOimlLCaPLckHM2jnuvbVUjSuZbGUs15PUyefjz7t-bHnojwYp0TPDXah0FZwHWe0nhsmo2kEiC9fxmhzya3bQYRsLHMWw9v7Ugt4wCzdQXiclLdEbTEZ2s_odiZe-vj3pDG9MJ1gDCZ1l5ajMb3BN1c3L8ODsSLmqc'
    },
    {
        name: 'Latina',
        slug: 'latina',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuA7gY2rMtr_fSF1n2aeF1jWyPwpCas0JbNAeeZPrSQOcHxyZEjMs_U1yzSzHBzBMBq2vN4KKdcg2kujejDR2UAshHiTSdSiNJNB95FckaFVaU2wcH6BLqAG4u04KtAZY9R8ovvaHXftcVzxklRp8x2JgEJLvx37C6hIPZX0YZyg00sHsuOx65eOhHb9vz2XzeczA2oHR2lPc9yBTAfgxYF6JCBFLFdHgd-U6lbgugoL53Y85orncyjUvqRShrMRUnDwObdAH9lVrfNd'
    },
    {
        name: 'Amateur',
        slug: 'amateur',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBf2N2pLIYjyWHFQ6HGO97OAKvHNuktfcICn-4o6q2CRfU4D16K5tuwbrM7ySgZBMKnXlGWGOLGO0W4qLv-uNRhGKsdHvsvRQxBrACW4E152Lj3Dh-vykrTDm9Y7DtNwHptp3TyRQwqBKaZMSP4FmT99T2l1ay0SoQE24LT5Y85p3kpiF-o4-zQkHKoWFjQLAQctxRKgkJJcvhgUdR_pC3NEhRWwmHN_ogRpQA7tP5YagV0jr3hOE9XbAqaCPfZrTog0ihGFhfWRAba'
    },
    {
        name: 'Teen',
        slug: 'teen',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuA1DgnjUG2m78BS31Hh_p0_aEiNnUdgYeo3bAJvnDuadnvu1Mn-M7m9C84SIzavcd6jMIlC87tuCUvjAYE6s3kssrgx3vVWJafX1bzWjktOK9w4-dZ4fFuiELj7DzntR2T6L6AY3ak58qf5sTE0bRDUuS0ImhVGUAdYAT4JmImmwG8i9v7DCFj3_BtTneJdOHtGIDGt9M32j4L8vz65GXs1FAT6QG7Dem70WL-vPzr_GozEIW7H1dVr1shGYfJf5fHWCxO52znTuqk8'
    },
    {
        name: 'Mature',
        slug: 'mature',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuB399n5jsMLZP3E521_95YGyUj-JYCbAJRoMTXx8YoEhJ47CYdy-td9lfDhgXJjosQbXvV9QDYTroRoSP9m_vV4ipPQIUyRVcK8gsN4nF7KibY6SnI9Qc7AxKL3hXpe_o505GQAjhX0wOo1bOqwx3k4BUtuFervKeoA-VWP-h_JW2jASYcbsQrGVTvZbqr4WgqerKhHvtbbDUrunP_PvHheg9m1bxvp77iCQlk2iWLruLxfpdS0ymglBjANZfdtU4kPS8TXqKP39K3B'
    },
    {
        name: 'Fetish',
        slug: 'fetish',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBf-LU36CTb5r5ts_sQCH41lTXcfGFz1ml_iNqKQQBoD0BtBb9QzRH8lTMFBiZdbZM9xjchoTVx24hBy0KVfPqj5lIvHCk_Qd4-DDj-W6s3SnbwXXaM7BohcTIElYOb9VZjBtsE3PX7Sok1ha7Ni-k8iLrR1kzragF5DwKmmopPgsY6NoODvRe4oTYd_7b6Xt0vxuxPMclfwZ0Lem-4Ky2UAykKc6FyAjRrSJPfuj5ADZcXPY_-3momBEh8N9chZ56kFoI7SYKcY69V'
    },
    {
        name: 'Gay',
        slug: 'gay',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuAxwTW-zXUzUx_Usv5ZRAgn6gW6ZcJsbbXR8Id4piLm4wEy3F6eilSkZnvQp2ImmqDGzxm9mtUjnION_jxCuXsNsaGCPxLptAdJ8H6guWmzhpvnhPbSf-rRlriQE5np_bqdW3BWwzm6zyKd0UUTWMYzSusBsPRfAUqojCD-AD2vVUMzYHVCC3tJannfVnSvEg65Co94-2zMysoXEPE_ZB88-9wkH5jmVcOpyRsT27V0GdcbTd6nYHlx3BXWmnylM2O0BzX-iU6iruSi'
    },
    {
        name: 'Lesbian',
        slug: 'lesbian',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuA7xQu0FWYiw_vrF5lwSn5UgxhSRYcJyZWisEsOqVlUPPmGd9oaMeS-S3_zq7nit3La7ZrZn4bDf6v_8K2IVfmB9lwVs7UQM7salTbgRHRO-eO9DKbeulQTlw7xa0y_M_gN7RRIpuwO81xAa3VP6ijxcbWASQM4uR6TxUi8BMqjIsi1Oi12hv9JI0wGeuPjSLyJXzs4zHIQQTW3dF0DrPYI29qJG6gkOwnAjtEXpFYe2FO5y4rqfKg3EhXSMSycfbTk8-NbHNgC7eHe'
    },
    {
        name: 'Transgender',
        slug: 'transgender',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuA8YwPHRUZNPcf1Ab6FrvfXWLLoiTeLzD-GpIsP8PPCz0dXO6G6NSXWSxox11bzLt-gwDPJieIeUFv0ZgkfPrRf5Yc-8KtUUFdzr3P1W998eWxSpptQ3XuSPYJEh9eEmOOST_NZyEJRsDy45fYmwwpiYwkjjSxr19tsOIZiON5_nPU4dbSAMKchh6EJl1NXMZ7QjSkckQSVje5Gp-Ve1C0IUonXe6-e0tMX63W7BPEypLAQJWRl_PyQfs7saMhy_27BXnrhl40k1PQ4'
    }
];
function getCategoryBySlug(slug) {
    return categories.find((cat)=>cat.slug === slug);
}
function getVideosByCategory(categorySlug) {
    const category = getCategoryBySlug(categorySlug);
    if (!category) return [];
    // For now, return all videos. In a real app, this would filter by category
    return mockVideos;
}
function sortVideosByRecent(videos) {
    return [
        ...videos
    ].sort((a, b)=>b.uploadedAt.getTime() - a.uploadedAt.getTime());
}
function sortVideosByPopular(videos) {
    return [
        ...videos
    ].sort((a, b)=>b.views - a.views);
}
function isValidCategorySlug(slug) {
    return categories.some((cat)=>cat.slug === slug);
}
}}),
"[project]/src/app/categories/[category]/page.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>CategoryPage),
    "generateMetadata": (()=>generateMetadata)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$api$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/api/navigation.react-server.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/components/navigation.react-server.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$category$2f$CategoryLayout$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/category/CategoryLayout.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$category$2f$CategoryPageContent$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/category/CategoryPageContent.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockData$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mockData.ts [app-rsc] (ecmascript)");
;
;
;
;
;
async function generateMetadata({ params }) {
    const { category } = await params;
    // Validate category
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockData$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isValidCategorySlug"])(category)) {
        return {
            title: 'Category Not Found - AdultStream',
            description: 'The requested category could not be found.'
        };
    }
    const categoryData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockData$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCategoryBySlug"])(category);
    if (!categoryData) {
        return {
            title: 'Category Not Found - AdultStream',
            description: 'The requested category could not be found.'
        };
    }
    const videos = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockData$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getVideosByCategory"])(category);
    const videoCount = videos.length;
    const title = `${categoryData.name} Videos - AdultStream`;
    const description = `Explore ${videoCount} premium ${categoryData.name.toLowerCase()} videos on AdultStream. High-quality content updated daily with the latest ${categoryData.name.toLowerCase()} videos.`;
    const url = `https://adultstream.com/categories/${category}`;
    return {
        title,
        description,
        keywords: [
            categoryData.name.toLowerCase(),
            'adult videos',
            'streaming',
            'premium content',
            'HD videos',
            'adult entertainment'
        ],
        authors: [
            {
                name: 'AdultStream'
            }
        ],
        creator: 'AdultStream',
        publisher: 'AdultStream',
        robots: {
            index: false,
            follow: false
        },
        openGraph: {
            type: 'website',
            locale: 'en_US',
            url,
            siteName: 'AdultStream',
            title,
            description,
            images: [
                {
                    url: categoryData.image,
                    width: 1200,
                    height: 630,
                    alt: `${categoryData.name} category on AdultStream`
                }
            ]
        },
        twitter: {
            card: 'summary_large_image',
            title,
            description,
            images: [
                categoryData.image
            ]
        },
        alternates: {
            canonical: url
        }
    };
}
async function CategoryPage({ params }) {
    const { category } = await params;
    // Validate category parameter
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockData$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isValidCategorySlug"])(category)) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["notFound"])();
    }
    // Get category data
    const categoryData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockData$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCategoryBySlug"])(category);
    if (!categoryData) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["notFound"])();
    }
    // Get videos for this category
    const videos = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockData$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getVideosByCategory"])(category);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$category$2f$CategoryLayout$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["CategoryLayout"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$category$2f$CategoryPageContent$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["CategoryPageContent"], {
            category: categoryData,
            videos: videos
        }, void 0, false, {
            fileName: "[project]/src/app/categories/[category]/page.tsx",
            lineNumber: 106,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/categories/[category]/page.tsx",
        lineNumber: 105,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/app/categories/[category]/page.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/categories/[category]/page.tsx [app-rsc] (ecmascript)"));
}}),

};

//# sourceMappingURL=_3c33e36b._.js.map