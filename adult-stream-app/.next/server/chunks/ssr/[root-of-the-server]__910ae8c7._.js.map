{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/release/adult-stream-app/src/components/category/MobileMenu.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\n\ninterface MobileMenuProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nexport function MobileMenu({ isOpen, onClose }: MobileMenuProps) {\n  if (!isOpen) return null;\n\n  return (\n    <>\n      {/* Backdrop */}\n      <div \n        className=\"fixed inset-0 bg-black/50 z-40 md:hidden\"\n        onClick={onClose}\n      />\n      \n      {/* Menu Panel */}\n      <div className=\"fixed top-0 right-0 h-full w-64 bg-[#1A0B0C] border-l border-[#391D1F] z-50 md:hidden\">\n        <div className=\"flex flex-col h-full\">\n          {/* Header */}\n          <div className=\"flex items-center justify-between p-4 border-b border-[#391D1F]\">\n            <h3 className=\"text-white font-semibold\">Menu</h3>\n            <button\n              onClick={onClose}\n              className=\"text-gray-400 hover:text-white p-2\"\n              aria-label=\"Close menu\"\n            >\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n          </div>\n\n          {/* Navigation Links */}\n          <nav className=\"flex-1 p-4\">\n            <div className=\"space-y-4\">\n              <Link \n                href=\"/\"\n                className=\"block text-gray-300 hover:text-white py-2 transition-colors\"\n                onClick={onClose}\n              >\n                Home\n              </Link>\n              <Link \n                href=\"/categories\"\n                className=\"block text-gray-300 hover:text-white py-2 transition-colors\"\n                onClick={onClose}\n              >\n                Categories\n              </Link>\n              <Link \n                href=\"#\"\n                className=\"block text-gray-300 hover:text-white py-2 transition-colors\"\n                onClick={onClose}\n              >\n                Models\n              </Link>\n              <Link \n                href=\"#\"\n                className=\"block text-gray-300 hover:text-white py-2 transition-colors\"\n                onClick={onClose}\n              >\n                Premium\n              </Link>\n              \n              {/* Divider */}\n              <div className=\"border-t border-[#391D1F] my-4\" />\n              \n              <Link \n                href=\"#\"\n                className=\"block text-gray-300 hover:text-white py-2 transition-colors\"\n                onClick={onClose}\n              >\n                Favorites\n              </Link>\n              <Link \n                href=\"#\"\n                className=\"block text-gray-300 hover:text-white py-2 transition-colors\"\n                onClick={onClose}\n              >\n                Watch Later\n              </Link>\n              <Link \n                href=\"#\"\n                className=\"block text-gray-300 hover:text-white py-2 transition-colors\"\n                onClick={onClose}\n              >\n                History\n              </Link>\n            </div>\n          </nav>\n\n          {/* Footer */}\n          <div className=\"p-4 border-t border-[#391D1F]\">\n            <button className=\"w-full bg-[#E92933] hover:bg-[#d9202a] text-white font-semibold py-2 px-4 rounded-lg transition-colors\">\n              Sign In\n            </button>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAUO,SAAS,WAAW,EAAE,MAAM,EAAE,OAAO,EAAmB;IAC7D,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE;;0BAEE,8OAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAIX,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2B;;;;;;8CACzC,8OAAC;oCACC,SAAS;oCACT,WAAU;oCACV,cAAW;8CAEX,cAAA,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;sCAM3E,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS;kDACV;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS;kDACV;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS;kDACV;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS;kDACV;;;;;;kDAKD,8OAAC;wCAAI,WAAU;;;;;;kDAEf,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS;kDACV;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS;kDACV;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS;kDACV;;;;;;;;;;;;;;;;;sCAOL,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAO,WAAU;0CAAyG;;;;;;;;;;;;;;;;;;;;;;;;AAQvI", "debugId": null}}, {"offset": {"line": 238, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/release/adult-stream-app/src/components/category/CategoryHeader.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { MobileMenu } from './MobileMenu';\n\ninterface CategoryHeaderProps {\n  className?: string;\n}\n\nexport function CategoryHeader({ className }: CategoryHeaderProps) {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  return (\n    <>\n    <header className={`flex items-center justify-between whitespace-nowrap border-b border-solid border-[#391D1F] px-4 sm:px-6 lg:px-10 py-3 ${className || ''}`}>\n      {/* Left side - Logo and Navigation */}\n      <div className=\"flex items-center gap-4 md:gap-8\">\n        {/* Logo */}\n        <Link href=\"/\" className=\"flex items-center gap-2 text-white\">\n          <svg className=\"h-6 w-6 text-[#E92933]\" fill=\"none\" viewBox=\"0 0 48 48\" xmlns=\"http://www.w3.org/2000/svg\">\n            <path\n              clipRule=\"evenodd\"\n              d=\"M24 4H42V17.3333V30.6667H24V44H6V30.6667V17.3333H24V4Z\"\n              fill=\"currentColor\"\n              fillRule=\"evenodd\"\n            />\n          </svg>\n          <h2 className=\"text-white text-xl font-bold leading-tight tracking-[-0.015em]\">Streamr</h2>\n        </Link>\n\n        {/* Navigation - Hidden on mobile */}\n        <nav className=\"hidden md:flex items-center gap-6\">\n          <Link\n            className=\"text-gray-300 hover:text-white text-sm font-medium leading-normal transition-colors\"\n            href=\"/\"\n          >\n            Home\n          </Link>\n          <Link\n            className=\"text-gray-300 hover:text-white text-sm font-medium leading-normal transition-colors\"\n            href=\"/categories\"\n          >\n            Categories\n          </Link>\n          <Link\n            className=\"text-gray-300 hover:text-white text-sm font-medium leading-normal transition-colors\"\n            href=\"#\"\n          >\n            Models\n          </Link>\n          <Link\n            className=\"text-gray-300 hover:text-white text-sm font-medium leading-normal transition-colors\"\n            href=\"#\"\n          >\n            Premium\n          </Link>\n        </nav>\n      </div>\n\n      {/* Right side - Search, Bookmarks, User */}\n      <div className=\"flex flex-1 items-center justify-end gap-2 sm:gap-4\">\n        {/* Search Bar */}\n        <label className=\"relative flex-1 max-w-xs\">\n          <div className=\"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3\">\n            <svg className=\"h-5 w-5 text-[#C89295]\" fill=\"currentColor\" height=\"24px\" viewBox=\"0 0 256 256\" width=\"24px\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z\" />\n            </svg>\n          </div>\n          <input\n            className=\"form-input block w-full rounded-lg border-none bg-[#391D1F] py-2.5 pl-10 pr-3 text-white placeholder:text-[#C89295] focus:outline-none focus:ring-2 focus:ring-[#E92933] focus:ring-offset-2 focus:ring-offset-[#1A0B0C] sm:text-sm\"\n            placeholder=\"Search videos...\"\n            type=\"text\"\n          />\n        </label>\n\n        {/* Bookmarks Button */}\n        <button\n          className=\"flex items-center justify-center rounded-lg p-2.5 text-white transition-colors hover:bg-[#391D1F] focus:outline-none focus:ring-2 focus:ring-[#E92933] focus:ring-offset-2 focus:ring-offset-[#1A0B0C]\"\n          aria-label=\"Bookmarks\"\n        >\n          <svg fill=\"currentColor\" height=\"20px\" viewBox=\"0 0 256 256\" width=\"20px\" xmlns=\"http://www.w3.org/2000/svg\">\n            <path d=\"M184,32H72A16,16,0,0,0,56,48V224a8,8,0,0,0,12.24,6.78L128,193.43l59.77,37.35A8,8,0,0,0,200,224V48A16,16,0,0,0,184,32Zm0,177.57-51.77-32.35a8,8,0,0,0-8.48,0L72,209.57V48H184Z\" />\n          </svg>\n        </button>\n\n        {/* User Avatar */}\n        <div\n          className=\"aspect-square size-10 rounded-full bg-cover bg-center bg-no-repeat\"\n          style={{\n            backgroundImage: 'url(\"https://lh3.googleusercontent.com/aida-public/AB6AXuBrgo5NeXaf1PG9RgXgIDK3n5IPlb1GgzT75g1TjaBc0tAUMmUFsRjjTEIXpfsiCEBqkNPgniynMop2KNEbUBljuJlMbP6n_vkbsloyQSZVm0gixenHSGFS5-l8jkYeUaTme6FsAsT9iaNWj5h3M4oO7uBSZxHz2qf44FvvOan7QOnGQql8Gx74w4UCL70LlrKCxkil9dZD1IUd6BKKL5IKtQLbHmK1JY1E9vSfFUkFUsECZPI6qvLEFDBbsU1abUikZViSSqdXcZHx\")'\n          }}\n        />\n\n        {/* Mobile Menu Button */}\n        <button\n          className=\"md:hidden text-gray-300 hover:text-white p-2.5\"\n          aria-label=\"Open menu\"\n          onClick={() => setIsMobileMenuOpen(true)}\n        >\n          <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n            <path d=\"M4 6h16M4 12h16m-7 6h7\" strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" />\n          </svg>\n        </button>\n      </div>\n    </header>\n\n    {/* Mobile Menu */}\n    <MobileMenu\n      isOpen={isMobileMenuOpen}\n      onClose={() => setIsMobileMenuOpen(false)}\n    />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAUO,SAAS,eAAe,EAAE,SAAS,EAAuB;IAC/D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,qBACE;;0BACA,8OAAC;gBAAO,WAAW,CAAC,sHAAsH,EAAE,aAAa,IAAI;;kCAE3J,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCAAI,WAAU;wCAAyB,MAAK;wCAAO,SAAQ;wCAAY,OAAM;kDAC5E,cAAA,8OAAC;4CACC,UAAS;4CACT,GAAE;4CACF,MAAK;4CACL,UAAS;;;;;;;;;;;kDAGb,8OAAC;wCAAG,WAAU;kDAAiE;;;;;;;;;;;;0CAIjF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,WAAU;wCACV,MAAK;kDACN;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,WAAU;wCACV,MAAK;kDACN;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,WAAU;wCACV,MAAK;kDACN;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,WAAU;wCACV,MAAK;kDACN;;;;;;;;;;;;;;;;;;kCAOL,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAM,WAAU;;kDACf,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;4CAAyB,MAAK;4CAAe,QAAO;4CAAO,SAAQ;4CAAc,OAAM;4CAAO,OAAM;sDACjH,cAAA,8OAAC;gDAAK,GAAE;;;;;;;;;;;;;;;;kDAGZ,8OAAC;wCACC,WAAU;wCACV,aAAY;wCACZ,MAAK;;;;;;;;;;;;0CAKT,8OAAC;gCACC,WAAU;gCACV,cAAW;0CAEX,cAAA,8OAAC;oCAAI,MAAK;oCAAe,QAAO;oCAAO,SAAQ;oCAAc,OAAM;oCAAO,OAAM;8CAC9E,cAAA,8OAAC;wCAAK,GAAE;;;;;;;;;;;;;;;;0CAKZ,8OAAC;gCACC,WAAU;gCACV,OAAO;oCACL,iBAAiB;gCACnB;;;;;;0CAIF,8OAAC;gCACC,WAAU;gCACV,cAAW;gCACX,SAAS,IAAM,oBAAoB;0CAEnC,cAAA,8OAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;oCAAY,OAAM;8CACnF,cAAA,8OAAC;wCAAK,GAAE;wCAAyB,eAAc;wCAAQ,gBAAe;wCAAQ,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOlG,8OAAC,4IAAA,CAAA,aAAU;gBACT,QAAQ;gBACR,SAAS,IAAM,oBAAoB;;;;;;;;AAIzC", "debugId": null}}, {"offset": {"line": 491, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/release/adult-stream-app/src/components/category/CategoryLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { CategoryHeader } from './CategoryHeader';\n\ninterface CategoryLayoutProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport function CategoryLayout({ children, className }: CategoryLayoutProps) {\n  return (\n    <div\n      className={`relative flex size-full min-h-screen flex-col group/design-root overflow-x-hidden bg-[#1A0B0C] dark ${className || ''}`}\n      style={{ fontFamily: '\"Plus Jakarta Sans\", \"Noto Sans\", sans-serif' }}\n    >\n      <div className=\"layout-container flex h-full grow flex-col\">\n        {/* Header */}\n        <CategoryHeader />\n\n        {/* Main Content */}\n        <main className=\"flex flex-1 justify-center py-5 px-4 sm:px-6 lg:px-8\">\n          <div className=\"layout-content-container w-full max-w-screen-xl flex-col\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAUO,SAAS,eAAe,EAAE,QAAQ,EAAE,SAAS,EAAuB;IACzE,qBACE,8OAAC;QACC,WAAW,CAAC,oGAAoG,EAAE,aAAa,IAAI;QACnI,OAAO;YAAE,YAAY;QAA+C;kBAEpE,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,gJAAA,CAAA,iBAAc;;;;;8BAGf,8OAAC;oBAAK,WAAU;8BACd,cAAA,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 546, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/release/adult-stream-app/src/components/category/CategoryError.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\n\ninterface CategoryErrorProps {\n  error?: Error;\n  reset?: () => void;\n  categoryName?: string;\n}\n\nexport function CategoryError({ error, reset, categoryName }: CategoryErrorProps) {\n  return (\n    <div className=\"flex flex-col items-center justify-center min-h-[400px] text-center p-8\">\n      {/* Error Icon */}\n      <div className=\"mb-6\">\n        <svg \n          className=\"w-20 h-20 text-red-500 mx-auto\" \n          fill=\"none\" \n          stroke=\"currentColor\" \n          viewBox=\"0 0 24 24\"\n        >\n          <path \n            strokeLinecap=\"round\" \n            strokeLinejoin=\"round\" \n            strokeWidth={1.5} \n            d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z\" \n          />\n        </svg>\n      </div>\n\n      {/* Error Message */}\n      <h2 className=\"text-2xl font-bold text-white mb-4\">\n        {categoryName ? `Error Loading ${categoryName} Category` : 'Something went wrong'}\n      </h2>\n      \n      <p className=\"text-gray-400 mb-8 max-w-md\">\n        {error?.message || 'We encountered an error while loading this category. Please try again or browse other categories.'}\n      </p>\n\n      {/* Action Buttons */}\n      <div className=\"flex flex-col sm:flex-row gap-4\">\n        {reset && (\n          <button\n            onClick={reset}\n            className=\"bg-[#E92933] hover:bg-[#d9202a] text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200\"\n          >\n            Try Again\n          </button>\n        )}\n        \n        <Link\n          href=\"/categories\"\n          className=\"bg-[#391D1F] hover:bg-[#4A2528] text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200\"\n        >\n          Browse Categories\n        </Link>\n        \n        <Link\n          href=\"/\"\n          className=\"text-gray-400 hover:text-white font-medium py-3 px-6 transition-colors duration-200\"\n        >\n          Go Home\n        </Link>\n      </div>\n\n      {/* Additional Help */}\n      <div className=\"mt-8 text-sm text-gray-500\">\n        <p>If this problem persists, please contact our support team.</p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAWO,SAAS,cAAc,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAsB;IAC9E,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,WAAU;oBACV,MAAK;oBACL,QAAO;oBACP,SAAQ;8BAER,cAAA,8OAAC;wBACC,eAAc;wBACd,gBAAe;wBACf,aAAa;wBACb,GAAE;;;;;;;;;;;;;;;;0BAMR,8OAAC;gBAAG,WAAU;0BACX,eAAe,CAAC,cAAc,EAAE,aAAa,SAAS,CAAC,GAAG;;;;;;0BAG7D,8OAAC;gBAAE,WAAU;0BACV,OAAO,WAAW;;;;;;0BAIrB,8OAAC;gBAAI,WAAU;;oBACZ,uBACC,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;kCAKH,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCACX;;;;;;kCAID,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;;0BAMH,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;8BAAE;;;;;;;;;;;;;;;;;AAIX", "debugId": null}}, {"offset": {"line": 664, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/release/adult-stream-app/src/app/categories/%5Bcategory%5D/error.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { CategoryLayout } from '@/components/category/CategoryLayout';\nimport { CategoryError } from '@/components/category/CategoryError';\n\ninterface ErrorPageProps {\n  error: Error & { digest?: string };\n  reset: () => void;\n}\n\nexport default function CategoryErrorPage({ error, reset }: ErrorPageProps) {\n  // Log error for debugging\n  React.useEffect(() => {\n    console.error('Category page error:', error);\n  }, [error]);\n\n  return (\n    <CategoryLayout>\n      <CategoryError \n        error={error}\n        reset={reset}\n      />\n    </CategoryLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAWe,SAAS,kBAAkB,EAAE,KAAK,EAAE,KAAK,EAAkB;IACxE,0BAA0B;IAC1B,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,QAAQ,KAAK,CAAC,wBAAwB;IACxC,GAAG;QAAC;KAAM;IAEV,qBACE,8OAAC,gJAAA,CAAA,iBAAc;kBACb,cAAA,8OAAC,+IAAA,CAAA,gBAAa;YACZ,OAAO;YACP,OAAO;;;;;;;;;;;AAIf", "debugId": null}}]}