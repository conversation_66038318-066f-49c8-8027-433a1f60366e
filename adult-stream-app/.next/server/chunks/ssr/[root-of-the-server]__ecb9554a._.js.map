{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/plus_jakarta_sans_6f4309c.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"plus_jakarta_sans_6f4309c-module__m-KqFG__className\",\n  \"variable\": \"plus_jakarta_sans_6f4309c-module__m-KqFG__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/plus_jakarta_sans_6f4309c.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Plus_Jakarta_Sans%22,%22arguments%22:[{%22variable%22:%22--font-plus-jakarta%22,%22subsets%22:[%22latin%22],%22weight%22:[%22400%22,%22500%22,%22600%22,%22700%22,%22800%22],%22display%22:%22swap%22}],%22variableName%22:%22plusJakartaSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Plus Jakarta Sans', 'Plus Jakarta Sans Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,gKAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,gKAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,gKAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/noto_sans_b2581ef.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"noto_sans_b2581ef-module__vHNLYG__className\",\n  \"variable\": \"noto_sans_b2581ef-module__vHNLYG__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/noto_sans_b2581ef.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Noto_Sans%22,%22arguments%22:[{%22variable%22:%22--font-noto-sans%22,%22subsets%22:[%22latin%22],%22weight%22:[%22400%22,%22500%22,%22700%22,%22900%22],%22display%22:%22swap%22}],%22variableName%22:%22notoSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Noto Sans', 'Noto Sans Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,wJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,wJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,wJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/release/adult-stream-app/src/app/layout.tsx"], "sourcesContent": ["import type { Metada<PERSON> } from \"next\";\nimport { Plus_Jakarta_Sans, Noto_Sans } from \"next/font/google\";\nimport \"./globals.css\";\n\nconst plusJakartaSans = Plus_Jakarta_Sans({\n  variable: \"--font-plus-jakarta\",\n  subsets: [\"latin\"],\n  weight: [\"400\", \"500\", \"600\", \"700\", \"800\"],\n  display: \"swap\",\n});\n\nconst notoSans = Noto_Sans({\n  variable: \"--font-noto-sans\",\n  subsets: [\"latin\"],\n  weight: [\"400\", \"500\", \"700\", \"900\"],\n  display: \"swap\",\n});\n\nexport const metadata: Metadata = {\n  title: \"AdultStream - Premium Video Hub\",\n  description: \"Discover premium adult content with AdultStream. Browse categories, watch videos, and enjoy a seamless streaming experience.\",\n  keywords: [\"adult\", \"streaming\", \"videos\", \"entertainment\", \"premium\"],\n  authors: [{ name: \"AdultStream\" }],\n  creator: \"AdultStream\",\n  publisher: \"AdultStream\",\n  robots: {\n    index: false,\n    follow: false,\n  },\n  viewport: {\n    width: \"device-width\",\n    initialScale: 1,\n    maximumScale: 1,\n  },\n  themeColor: \"#E92933\",\n  colorScheme: \"dark\",\n  openGraph: {\n    type: \"website\",\n    locale: \"en_US\",\n    url: \"https://adultstream.com\",\n    siteName: \"AdultStream\",\n    title: \"AdultStream - Premium Video Hub\",\n    description: \"Discover premium adult content with AdultStream. Browse categories, watch videos, and enjoy a seamless streaming experience.\",\n    images: [\n      {\n        url: \"/og-image.jpg\",\n        width: 1200,\n        height: 630,\n        alt: \"AdultStream\",\n      },\n    ],\n  },\n  twitter: {\n    card: \"summary_large_image\",\n    title: \"AdultStream - Premium Video Hub\",\n    description: \"Discover premium adult content with AdultStream. Browse categories, watch videos, and enjoy a seamless streaming experience.\",\n    images: [\"/og-image.jpg\"],\n  },\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\" className=\"dark\">\n      <head>\n        <link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossOrigin=\"\" />\n        <link rel=\"icon\" href=\"/favicon.ico\" />\n        <script src=\"https://cdn.tailwindcss.com?plugins=forms,container-queries\"></script>\n      </head>\n      <body\n        className={`${plusJakartaSans.variable} ${notoSans.variable} bg-background text-text antialiased`}\n      >\n        <div className=\"relative flex min-h-screen flex-col overflow-x-hidden\">\n          {children}\n        </div>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAkBO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;QAAC;QAAS;QAAa;QAAU;QAAiB;KAAU;IACtE,SAAS;QAAC;YAAE,MAAM;QAAc;KAAE;IAClC,SAAS;IACT,WAAW;IACX,QAAQ;QACN,OAAO;QACP,QAAQ;IACV;IACA,UAAU;QACR,OAAO;QACP,cAAc;QACd,cAAc;IAChB;IACA,YAAY;IACZ,aAAa;IACb,WAAW;QACT,MAAM;QACN,QAAQ;QACR,KAAK;QACL,UAAU;QACV,OAAO;QACP,aAAa;QACb,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;YACP;SACD;IACH;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,QAAQ;YAAC;SAAgB;IAC3B;AACF;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;QAAK,WAAU;;0BACxB,8OAAC;;kCACC,8OAAC;wBAAK,KAAI;wBAAa,MAAK;wBAA4B,aAAY;;;;;;kCACpE,8OAAC;wBAAK,KAAI;wBAAO,MAAK;;;;;;kCACtB,8OAAC;wBAAO,KAAI;;;;;;;;;;;;0BAEd,8OAAC;gBACC,WAAW,GAAG,oJAAA,CAAA,UAAe,CAAC,QAAQ,CAAC,CAAC,EAAE,4IAAA,CAAA,UAAQ,CAAC,QAAQ,CAAC,oCAAoC,CAAC;0BAEjG,cAAA,8OAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 197, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/release/adult-stream-app/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}