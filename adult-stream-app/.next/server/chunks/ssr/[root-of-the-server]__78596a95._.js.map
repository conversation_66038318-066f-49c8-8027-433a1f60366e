{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/release/adult-stream-app/src/components/category/CategoryHeader.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\n\ninterface CategoryHeaderProps {\n  className?: string;\n}\n\nexport function CategoryHeader({ className }: CategoryHeaderProps) {\n  return (\n    <header className={`flex items-center justify-between whitespace-nowrap border-b border-solid border-[#391D1F] px-4 sm:px-6 lg:px-10 py-3 ${className || ''}`}>\n      {/* Left side - Logo and Navigation */}\n      <div className=\"flex items-center gap-4 md:gap-8\">\n        {/* Logo */}\n        <Link href=\"/\" className=\"flex items-center gap-2 text-white\">\n          <svg className=\"h-6 w-6 text-[#E92933]\" fill=\"none\" viewBox=\"0 0 48 48\" xmlns=\"http://www.w3.org/2000/svg\">\n            <path \n              clipRule=\"evenodd\" \n              d=\"M24 4H42V17.3333V30.6667H24V44H6V30.6667V17.3333H24V4Z\" \n              fill=\"currentColor\"\n              fillRule=\"evenodd\"\n            />\n          </svg>\n          <h2 className=\"text-white text-xl font-bold leading-tight tracking-[-0.015em]\">Streamr</h2>\n        </Link>\n\n        {/* Navigation - Hidden on mobile */}\n        <nav className=\"hidden md:flex items-center gap-6\">\n          <Link \n            className=\"text-gray-300 hover:text-white text-sm font-medium leading-normal transition-colors\" \n            href=\"/\"\n          >\n            Home\n          </Link>\n          <Link \n            className=\"text-gray-300 hover:text-white text-sm font-medium leading-normal transition-colors\" \n            href=\"/categories\"\n          >\n            Categories\n          </Link>\n          <Link \n            className=\"text-gray-300 hover:text-white text-sm font-medium leading-normal transition-colors\" \n            href=\"#\"\n          >\n            Models\n          </Link>\n          <Link \n            className=\"text-gray-300 hover:text-white text-sm font-medium leading-normal transition-colors\" \n            href=\"#\"\n          >\n            Premium\n          </Link>\n        </nav>\n      </div>\n\n      {/* Right side - Search, Bookmarks, User */}\n      <div className=\"flex flex-1 items-center justify-end gap-2 sm:gap-4\">\n        {/* Search Bar */}\n        <label className=\"relative flex-1 max-w-xs\">\n          <div className=\"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3\">\n            <svg className=\"h-5 w-5 text-[#C89295]\" fill=\"currentColor\" height=\"24px\" viewBox=\"0 0 256 256\" width=\"24px\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z\" />\n            </svg>\n          </div>\n          <input\n            className=\"form-input block w-full rounded-lg border-none bg-[#391D1F] py-2.5 pl-10 pr-3 text-white placeholder:text-[#C89295] focus:outline-none focus:ring-2 focus:ring-[#E92933] focus:ring-offset-2 focus:ring-offset-[#1A0B0C] sm:text-sm\"\n            placeholder=\"Search videos...\"\n            type=\"text\"\n          />\n        </label>\n\n        {/* Bookmarks Button */}\n        <button\n          className=\"flex items-center justify-center rounded-lg p-2.5 text-white transition-colors hover:bg-[#391D1F] focus:outline-none focus:ring-2 focus:ring-[#E92933] focus:ring-offset-2 focus:ring-offset-[#1A0B0C]\"\n          aria-label=\"Bookmarks\"\n        >\n          <svg fill=\"currentColor\" height=\"20px\" viewBox=\"0 0 256 256\" width=\"20px\" xmlns=\"http://www.w3.org/2000/svg\">\n            <path d=\"M184,32H72A16,16,0,0,0,56,48V224a8,8,0,0,0,12.24,6.78L128,193.43l59.77,37.35A8,8,0,0,0,200,224V48A16,16,0,0,0,184,32Zm0,177.57-51.77-32.35a8,8,0,0,0-8.48,0L72,209.57V48H184Z\" />\n          </svg>\n        </button>\n\n        {/* User Avatar */}\n        <div \n          className=\"aspect-square size-10 rounded-full bg-cover bg-center bg-no-repeat\"\n          style={{\n            backgroundImage: 'url(\"https://lh3.googleusercontent.com/aida-public/AB6AXuBrgo5NeXaf1PG9RgXgIDK3n5IPlb1GgzT75g1TjaBc0tAUMmUFsRjjTEIXpfsiCEBqkNPgniynMop2KNEbUBljuJlMbP6n_vkbsloyQSZVm0gixenHSGFS5-l8jkYeUaTme6FsAsT9iaNWj5h3M4oO7uBSZxHz2qf44FvvOan7QOnGQql8Gx74w4UCL70LlrKCxkil9dZD1IUd6BKKL5IKtQLbHmK1JY1E9vSfFUkFUsECZPI6qvLEFDBbsU1abUikZViSSqdXcZHx\")'\n          }}\n        />\n\n        {/* Mobile Menu Button */}\n        <button className=\"md:hidden text-gray-300 hover:text-white p-2.5\" aria-label=\"Open menu\">\n          <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n            <path d=\"M4 6h16M4 12h16m-7 6h7\" strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" />\n          </svg>\n        </button>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AASO,SAAS,eAAe,EAAE,SAAS,EAAuB;IAC/D,qBACE,8OAAC;QAAO,WAAW,CAAC,sHAAsH,EAAE,aAAa,IAAI;;0BAE3J,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAU;;0CACvB,8OAAC;gCAAI,WAAU;gCAAyB,MAAK;gCAAO,SAAQ;gCAAY,OAAM;0CAC5E,cAAA,8OAAC;oCACC,UAAS;oCACT,GAAE;oCACF,MAAK;oCACL,UAAS;;;;;;;;;;;0CAGb,8OAAC;gCAAG,WAAU;0CAAiE;;;;;;;;;;;;kCAIjF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,WAAU;gCACV,MAAK;0CACN;;;;;;0CAGD,8OAAC,4JAAA,CAAA,UAAI;gCACH,WAAU;gCACV,MAAK;0CACN;;;;;;0CAGD,8OAAC,4JAAA,CAAA,UAAI;gCACH,WAAU;gCACV,MAAK;0CACN;;;;;;0CAGD,8OAAC,4JAAA,CAAA,UAAI;gCACH,WAAU;gCACV,MAAK;0CACN;;;;;;;;;;;;;;;;;;0BAOL,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAM,WAAU;;0CACf,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAAyB,MAAK;oCAAe,QAAO;oCAAO,SAAQ;oCAAc,OAAM;oCAAO,OAAM;8CACjH,cAAA,8OAAC;wCAAK,GAAE;;;;;;;;;;;;;;;;0CAGZ,8OAAC;gCACC,WAAU;gCACV,aAAY;gCACZ,MAAK;;;;;;;;;;;;kCAKT,8OAAC;wBACC,WAAU;wBACV,cAAW;kCAEX,cAAA,8OAAC;4BAAI,MAAK;4BAAe,QAAO;4BAAO,SAAQ;4BAAc,OAAM;4BAAO,OAAM;sCAC9E,cAAA,8OAAC;gCAAK,GAAE;;;;;;;;;;;;;;;;kCAKZ,8OAAC;wBACC,WAAU;wBACV,OAAO;4BACL,iBAAiB;wBACnB;;;;;;kCAIF,8OAAC;wBAAO,WAAU;wBAAiD,cAAW;kCAC5E,cAAA,8OAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;4BAAY,OAAM;sCACnF,cAAA,8OAAC;gCAAK,GAAE;gCAAyB,eAAc;gCAAQ,gBAAe;gCAAQ,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMtG", "debugId": null}}, {"offset": {"line": 274, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/release/adult-stream-app/src/components/category/CategoryLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { CategoryHeader } from './CategoryHeader';\n\ninterface CategoryLayoutProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport function CategoryLayout({ children, className }: CategoryLayoutProps) {\n  return (\n    <div\n      className={`relative flex size-full min-h-screen flex-col group/design-root overflow-x-hidden bg-[#1A0B0C] dark ${className || ''}`}\n      style={{ fontFamily: '\"Plus Jakarta Sans\", \"Noto Sans\", sans-serif' }}\n    >\n      <div className=\"layout-container flex h-full grow flex-col\">\n        {/* Header */}\n        <CategoryHeader />\n\n        {/* Main Content */}\n        <main className=\"flex flex-1 justify-center py-5 px-4 sm:px-6 lg:px-8\">\n          <div className=\"layout-content-container w-full max-w-screen-xl flex-col\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAUO,SAAS,eAAe,EAAE,QAAQ,EAAE,SAAS,EAAuB;IACzE,qBACE,8OAAC;QACC,WAAW,CAAC,oGAAoG,EAAE,aAAa,IAAI;QACnI,OAAO;YAAE,YAAY;QAA+C;kBAEpE,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,gJAAA,CAAA,iBAAc;;;;;8BAGf,8OAAC;oBAAK,WAAU;8BACd,cAAA,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 329, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/release/adult-stream-app/src/components/category/CategoryTitle.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\n\ninterface CategoryTitleProps {\n  categoryName: string;\n  videoCount?: number;\n  className?: string;\n}\n\nexport function CategoryTitle({ categoryName, videoCount, className }: CategoryTitleProps) {\n  return (\n    <div className={`flex flex-wrap items-center justify-between gap-4 p-4 ${className || ''}`}>\n      <div className=\"flex flex-col gap-2\">\n        {/* Breadcrumb */}\n        <nav className=\"flex items-center space-x-2 text-sm\">\n          <Link \n            href=\"/\" \n            className=\"text-gray-400 hover:text-white transition-colors\"\n          >\n            Home\n          </Link>\n          <span className=\"text-gray-600\">/</span>\n          <Link \n            href=\"/categories\" \n            className=\"text-gray-400 hover:text-white transition-colors\"\n          >\n            Categories\n          </Link>\n          <span className=\"text-gray-600\">/</span>\n          <span className=\"text-white font-medium\">{categoryName}</span>\n        </nav>\n\n        {/* Category Title */}\n        <div className=\"flex items-center gap-3\">\n          <h1 className=\"text-white text-3xl font-bold leading-tight tracking-tight\">\n            Category: {categoryName}\n          </h1>\n          {videoCount !== undefined && (\n            <span className=\"bg-[#391D1F] text-gray-300 text-sm font-medium px-3 py-1 rounded-full\">\n              {videoCount} {videoCount === 1 ? 'video' : 'videos'}\n            </span>\n          )}\n        </div>\n      </div>\n\n      {/* Optional: Category Actions */}\n      <div className=\"flex items-center gap-2\">\n        {/* Subscribe/Follow Button */}\n        <button className=\"bg-[#E92933] hover:bg-[#d9202a] text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200\">\n          Follow Category\n        </button>\n        \n        {/* More Options */}\n        <button \n          className=\"text-gray-400 hover:text-white p-2 rounded-lg hover:bg-[#391D1F] transition-colors\"\n          aria-label=\"More options\"\n        >\n          <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path d=\"M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z\"/>\n          </svg>\n        </button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAWO,SAAS,cAAc,EAAE,YAAY,EAAE,UAAU,EAAE,SAAS,EAAsB;IACvF,qBACE,8OAAC;QAAI,WAAW,CAAC,sDAAsD,EAAE,aAAa,IAAI;;0BACxF,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;0CAChC,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;0CAChC,8OAAC;gCAAK,WAAU;0CAA0B;;;;;;;;;;;;kCAI5C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAA6D;oCAC9D;;;;;;;4BAEZ,eAAe,2BACd,8OAAC;gCAAK,WAAU;;oCACb;oCAAW;oCAAE,eAAe,IAAI,UAAU;;;;;;;;;;;;;;;;;;;0BAOnD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAO,WAAU;kCAA+G;;;;;;kCAKjI,8OAAC;wBACC,WAAU;wBACV,cAAW;kCAEX,cAAA,8OAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAe,SAAQ;sCACnD,cAAA,8OAAC;gCAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMpB", "debugId": null}}, {"offset": {"line": 487, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/release/adult-stream-app/src/components/category/CategoryTabs.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { SortOption } from '@/lib/types';\n\ninterface CategoryTabsProps {\n  activeTab: SortOption;\n  onTabChange: (tab: SortOption) => void;\n  className?: string;\n}\n\nexport function CategoryTabs({ activeTab, onTabChange, className }: CategoryTabsProps) {\n  const tabs: { key: SortOption; label: string }[] = [\n    { key: 'recent', label: 'Recent' },\n    { key: 'popular', label: 'Popular' }\n  ];\n\n  return (\n    <div className={`pb-3 ${className || ''}`}>\n      <div className=\"flex border-b border-[#4A2528] px-4 gap-6 sm:gap-8\">\n        {tabs.map((tab) => (\n          <button\n            key={tab.key}\n            onClick={() => onTabChange(tab.key)}\n            className={`flex flex-col items-center justify-center border-b-[3px] pb-3 pt-4 transition-colors ${\n              activeTab === tab.key\n                ? 'border-[#E92933] text-white'\n                : 'border-transparent text-[#C89295] hover:border-[#E92933]/50 hover:text-white'\n            }`}\n          >\n            <p className=\"text-sm font-semibold leading-normal tracking-[0.015em]\">\n              {tab.label}\n            </p>\n          </button>\n        ))}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAWO,SAAS,aAAa,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAqB;IACnF,MAAM,OAA6C;QACjD;YAAE,KAAK;YAAU,OAAO;QAAS;QACjC;YAAE,KAAK;YAAW,OAAO;QAAU;KACpC;IAED,qBACE,8OAAC;QAAI,WAAW,CAAC,KAAK,EAAE,aAAa,IAAI;kBACvC,cAAA,8OAAC;YAAI,WAAU;sBACZ,KAAK,GAAG,CAAC,CAAC,oBACT,8OAAC;oBAEC,SAAS,IAAM,YAAY,IAAI,GAAG;oBAClC,WAAW,CAAC,qFAAqF,EAC/F,cAAc,IAAI,GAAG,GACjB,gCACA,gFACJ;8BAEF,cAAA,8OAAC;wBAAE,WAAU;kCACV,IAAI,KAAK;;;;;;mBATP,IAAI,GAAG;;;;;;;;;;;;;;;AAgBxB", "debugId": null}}, {"offset": {"line": 541, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/release/adult-stream-app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatDuration(seconds: number): string {\n  const minutes = Math.floor(seconds / 60);\n  const remainingSeconds = seconds % 60;\n  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n}\n\nexport function formatViews(views: number): string {\n  if (views >= 1000000) {\n    return `${(views / 1000000).toFixed(1)}M`;\n  } else if (views >= 1000) {\n    return `${(views / 1000).toFixed(1)}K`;\n  }\n  return views.toString();\n}\n\nexport function formatTimeAgo(date: Date): string {\n  const now = new Date();\n  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n  \n  if (diffInSeconds < 60) {\n    return 'Just now';\n  } else if (diffInSeconds < 3600) {\n    const minutes = Math.floor(diffInSeconds / 60);\n    return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;\n  } else if (diffInSeconds < 86400) {\n    const hours = Math.floor(diffInSeconds / 3600);\n    return `${hours} hour${hours > 1 ? 's' : ''} ago`;\n  } else if (diffInSeconds < 604800) {\n    const days = Math.floor(diffInSeconds / 86400);\n    return `${days} day${days > 1 ? 's' : ''} ago`;\n  } else if (diffInSeconds < 2592000) {\n    const weeks = Math.floor(diffInSeconds / 604800);\n    return `${weeks} week${weeks > 1 ? 's' : ''} ago`;\n  } else if (diffInSeconds < 31536000) {\n    const months = Math.floor(diffInSeconds / 2592000);\n    return `${months} month${months > 1 ? 's' : ''} ago`;\n  } else {\n    const years = Math.floor(diffInSeconds / 31536000);\n    return `${years} year${years > 1 ? 's' : ''} ago`;\n  }\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,OAAe;IAC5C,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU;IACrC,MAAM,mBAAmB,UAAU;IACnC,OAAO,GAAG,QAAQ,CAAC,EAAE,iBAAiB,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;AACrE;AAEO,SAAS,YAAY,KAAa;IACvC,IAAI,SAAS,SAAS;QACpB,OAAO,GAAG,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IAC3C,OAAO,IAAI,SAAS,MAAM;QACxB,OAAO,GAAG,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IACxC;IACA,OAAO,MAAM,QAAQ;AACvB;AAEO,SAAS,cAAc,IAAU;IACtC,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI;IAEpE,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT,OAAO,IAAI,gBAAgB,MAAM;QAC/B,MAAM,UAAU,KAAK,KAAK,CAAC,gBAAgB;QAC3C,OAAO,GAAG,QAAQ,OAAO,EAAE,UAAU,IAAI,MAAM,GAAG,IAAI,CAAC;IACzD,OAAO,IAAI,gBAAgB,OAAO;QAChC,MAAM,QAAQ,KAAK,KAAK,CAAC,gBAAgB;QACzC,OAAO,GAAG,MAAM,KAAK,EAAE,QAAQ,IAAI,MAAM,GAAG,IAAI,CAAC;IACnD,OAAO,IAAI,gBAAgB,QAAQ;QACjC,MAAM,OAAO,KAAK,KAAK,CAAC,gBAAgB;QACxC,OAAO,GAAG,KAAK,IAAI,EAAE,OAAO,IAAI,MAAM,GAAG,IAAI,CAAC;IAChD,OAAO,IAAI,gBAAgB,SAAS;QAClC,MAAM,QAAQ,KAAK,KAAK,CAAC,gBAAgB;QACzC,OAAO,GAAG,MAAM,KAAK,EAAE,QAAQ,IAAI,MAAM,GAAG,IAAI,CAAC;IACnD,OAAO,IAAI,gBAAgB,UAAU;QACnC,MAAM,SAAS,KAAK,KAAK,CAAC,gBAAgB;QAC1C,OAAO,GAAG,OAAO,MAAM,EAAE,SAAS,IAAI,MAAM,GAAG,IAAI,CAAC;IACtD,OAAO;QACL,MAAM,QAAQ,KAAK,KAAK,CAAC,gBAAgB;QACzC,OAAO,GAAG,MAAM,KAAK,EAAE,QAAQ,IAAI,MAAM,GAAG,IAAI,CAAC;IACnD;AACF;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF", "debugId": null}}, {"offset": {"line": 617, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/release/adult-stream-app/src/components/category/CategoryVideoCard.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { Video } from '@/lib/types';\nimport { formatDuration, formatViews, formatTimeAgo } from '@/lib/utils';\n\ninterface CategoryVideoCardProps {\n  video: Video;\n  className?: string;\n  onClick?: () => void;\n}\n\nexport function CategoryVideoCard({ video, className, onClick }: CategoryVideoCardProps) {\n  const handleClick = () => {\n    if (onClick) {\n      onClick();\n    }\n  };\n\n  const cardContent = (\n    <div\n      className={`group relative aspect-video overflow-hidden rounded-lg bg-cover bg-center shadow-lg transition-all hover:scale-105 ${className || ''}`}\n      style={{\n        backgroundImage: `linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.1) 50%, transparent 100%), url(\"${video.thumbnail}\")`\n      }}\n    >\n      {/* Video Info Overlay */}\n      <div className=\"absolute inset-0 flex flex-col justify-end bg-gradient-to-t from-black/70 via-black/30 to-transparent p-3\">\n        <h3 className=\"text-sm font-bold text-white line-clamp-2 leading-tight\">\n          {video.title}\n        </h3>\n        <span className=\"mt-1 text-xs text-gray-400\">\n          {formatViews(video.views)} views • {formatTimeAgo(video.uploadedAt)}\n        </span>\n      </div>\n\n      {/* Play Button Overlay */}\n      <div className=\"absolute inset-0 flex items-center justify-center bg-black/50 opacity-0 transition-opacity group-hover:opacity-100\">\n        <svg className=\"h-12 w-12 text-white\" fill=\"currentColor\" viewBox=\"0 0 256 256\" xmlns=\"http://www.w3.org/2000/svg\">\n          <path d=\"M240,128a15.71,15.71,0,0,1-7.6,13.51L88.32,223.25a16,16,0,0,1-24.32-13.51V46.26a16,16,0,0,1,24.32-13.51L232.4,114.49A15.71,15.71,0,0,1,240,128Z\" />\n        </svg>\n      </div>\n\n      {/* Duration Badge */}\n      <div className=\"absolute bottom-2 right-2 bg-black/70 text-white text-xs font-medium px-2 py-1 rounded\">\n        {formatDuration(video.duration)}\n      </div>\n\n      {/* Quality Badges */}\n      <div className=\"absolute top-2 left-2 flex gap-1\">\n        {video.isHD && (\n          <span className=\"bg-[#E92933] text-white text-xs font-bold px-1.5 py-0.5 rounded\">\n            HD\n          </span>\n        )}\n        {video.isPremium && (\n          <span className=\"bg-yellow-500 text-black text-xs font-bold px-1.5 py-0.5 rounded\">\n            PREMIUM\n          </span>\n        )}\n      </div>\n\n      {/* Hover Actions */}\n      <div className=\"absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n        <div className=\"flex gap-1\">\n          {/* Favorite Button */}\n          <button\n            className=\"bg-black/60 hover:bg-black/80 text-white p-1.5 rounded-full transition-colors\"\n            onClick={(e) => {\n              e.preventDefault();\n              e.stopPropagation();\n              // Handle favorite action\n            }}\n            aria-label=\"Add to favorites\"\n          >\n            <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path d=\"M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z\"/>\n            </svg>\n          </button>\n\n          {/* Watch Later Button */}\n          <button\n            className=\"bg-black/60 hover:bg-black/80 text-white p-1.5 rounded-full transition-colors\"\n            onClick={(e) => {\n              e.preventDefault();\n              e.stopPropagation();\n              // Handle watch later action\n            }}\n            aria-label=\"Watch later\"\n          >\n            <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\n            </svg>\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n\n  if (onClick) {\n    return (\n      <div onClick={handleClick} className=\"cursor-pointer\">\n        {cardContent}\n      </div>\n    );\n  }\n\n  return (\n    <Link href={`/video/${video.id}`} className=\"block\">\n      {cardContent}\n    </Link>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAaO,SAAS,kBAAkB,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,EAA0B;IACrF,MAAM,cAAc;QAClB,IAAI,SAAS;YACX;QACF;IACF;IAEA,MAAM,4BACJ,8OAAC;QACC,WAAW,CAAC,mHAAmH,EAAE,aAAa,IAAI;QAClJ,OAAO;YACL,iBAAiB,CAAC,yFAAyF,EAAE,MAAM,SAAS,CAAC,EAAE,CAAC;QAClI;;0BAGA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCACX,MAAM,KAAK;;;;;;kCAEd,8OAAC;wBAAK,WAAU;;4BACb,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,MAAM,KAAK;4BAAE;4BAAU,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,UAAU;;;;;;;;;;;;;0BAKtE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;oBAAuB,MAAK;oBAAe,SAAQ;oBAAc,OAAM;8BACpF,cAAA,8OAAC;wBAAK,GAAE;;;;;;;;;;;;;;;;0BAKZ,8OAAC;gBAAI,WAAU;0BACZ,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,QAAQ;;;;;;0BAIhC,8OAAC;gBAAI,WAAU;;oBACZ,MAAM,IAAI,kBACT,8OAAC;wBAAK,WAAU;kCAAkE;;;;;;oBAInF,MAAM,SAAS,kBACd,8OAAC;wBAAK,WAAU;kCAAmE;;;;;;;;;;;;0BAOvF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BACC,WAAU;4BACV,SAAS,CAAC;gCACR,EAAE,cAAc;gCAChB,EAAE,eAAe;4BACjB,yBAAyB;4BAC3B;4BACA,cAAW;sCAEX,cAAA,8OAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAe,SAAQ;0CACnD,cAAA,8OAAC;oCAAK,GAAE;;;;;;;;;;;;;;;;sCAKZ,8OAAC;4BACC,WAAU;4BACV,SAAS,CAAC;gCACR,EAAE,cAAc;gCAChB,EAAE,eAAe;4BACjB,4BAA4B;4BAC9B;4BACA,cAAW;sCAEX,cAAA,8OAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAe,SAAQ;0CACnD,cAAA,8OAAC;oCAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQpB,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,SAAS;YAAa,WAAU;sBAClC;;;;;;IAGP;IAEA,qBACE,8OAAC,4JAAA,CAAA,UAAI;QAAC,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QAAE,WAAU;kBACzC;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 832, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/release/adult-stream-app/src/components/category/CategoryGrid.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Video } from '@/lib/types';\nimport { CategoryVideoCard } from './CategoryVideoCard';\n\ninterface CategoryGridProps {\n  videos: Video[];\n  loading?: boolean;\n  onLoadMore?: () => void;\n  hasMore?: boolean;\n  className?: string;\n}\n\n// Loading skeleton component\nfunction VideoCardSkeleton() {\n  return (\n    <div className=\"aspect-video bg-gray-700 rounded-lg animate-pulse\">\n      <div className=\"w-full h-full bg-gradient-to-t from-gray-800 to-gray-700 rounded-lg\">\n        {/* Skeleton content */}\n        <div className=\"absolute bottom-2 right-2 bg-gray-600 w-12 h-5 rounded animate-pulse\" />\n        <div className=\"absolute bottom-3 left-3 space-y-1\">\n          <div className=\"bg-gray-600 h-4 w-24 rounded animate-pulse\" />\n          <div className=\"bg-gray-600 h-3 w-16 rounded animate-pulse\" />\n        </div>\n      </div>\n    </div>\n  );\n}\n\n// Empty state component\nfunction EmptyState() {\n  return (\n    <div className=\"col-span-full flex flex-col items-center justify-center py-12 text-center\">\n      <div className=\"mb-4\">\n        <svg className=\"w-16 h-16 text-gray-600 mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z\" />\n        </svg>\n      </div>\n      <h3 className=\"text-lg font-semibold text-white mb-2\">No videos found</h3>\n      <p className=\"text-gray-400 max-w-sm\">\n        There are no videos in this category yet. Check back later for new content.\n      </p>\n    </div>\n  );\n}\n\nexport function CategoryGrid({ \n  videos, \n  loading = false, \n  onLoadMore, \n  hasMore = false, \n  className \n}: CategoryGridProps) {\n  // Show loading skeletons\n  if (loading && videos.length === 0) {\n    return (\n      <div className={`grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4 p-4 ${className || ''}`}>\n        {Array.from({ length: 12 }).map((_, index) => (\n          <VideoCardSkeleton key={index} />\n        ))}\n      </div>\n    );\n  }\n\n  // Show empty state\n  if (!loading && videos.length === 0) {\n    return (\n      <div className={`grid grid-cols-1 p-4 ${className || ''}`}>\n        <EmptyState />\n      </div>\n    );\n  }\n\n  return (\n    <div className={className}>\n      {/* Video Grid */}\n      <div className=\"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4 p-4\">\n        {videos.map((video) => (\n          <CategoryVideoCard\n            key={video.id}\n            video={video}\n          />\n        ))}\n        \n        {/* Loading more skeletons */}\n        {loading && videos.length > 0 && (\n          <>\n            {Array.from({ length: 6 }).map((_, index) => (\n              <VideoCardSkeleton key={`loading-${index}`} />\n            ))}\n          </>\n        )}\n      </div>\n\n      {/* Load More Button */}\n      {!loading && hasMore && onLoadMore && (\n        <div className=\"flex justify-center p-4\">\n          <button\n            onClick={onLoadMore}\n            className=\"bg-[#E92933] hover:bg-[#d9202a] text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200\"\n          >\n            Load More Videos\n          </button>\n        </div>\n      )}\n\n      {/* End of content indicator */}\n      {!loading && !hasMore && videos.length > 0 && (\n        <div className=\"text-center p-4\">\n          <p className=\"text-gray-400 text-sm\">\n            You've reached the end of this category\n          </p>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAIA;AAJA;;;AAcA,6BAA6B;AAC7B,SAAS;IACP,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKzB;AAEA,wBAAwB;AACxB,SAAS;IACP,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;oBAAkC,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACzF,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAK,GAAE;;;;;;;;;;;;;;;;0BAG3E,8OAAC;gBAAG,WAAU;0BAAwC;;;;;;0BACtD,8OAAC;gBAAE,WAAU;0BAAyB;;;;;;;;;;;;AAK5C;AAEO,SAAS,aAAa,EAC3B,MAAM,EACN,UAAU,KAAK,EACf,UAAU,EACV,UAAU,KAAK,EACf,SAAS,EACS;IAClB,yBAAyB;IACzB,IAAI,WAAW,OAAO,MAAM,KAAK,GAAG;QAClC,qBACE,8OAAC;YAAI,WAAW,CAAC,uFAAuF,EAAE,aAAa,IAAI;sBACxH,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAG,GAAG,GAAG,CAAC,CAAC,GAAG,sBAClC,8OAAC,uBAAuB;;;;;;;;;;IAIhC;IAEA,mBAAmB;IACnB,IAAI,CAAC,WAAW,OAAO,MAAM,KAAK,GAAG;QACnC,qBACE,8OAAC;YAAI,WAAW,CAAC,qBAAqB,EAAE,aAAa,IAAI;sBACvD,cAAA,8OAAC;;;;;;;;;;IAGP;IAEA,qBACE,8OAAC;QAAI,WAAW;;0BAEd,8OAAC;gBAAI,WAAU;;oBACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC,mJAAA,CAAA,oBAAiB;4BAEhB,OAAO;2BADF,MAAM,EAAE;;;;;oBAMhB,WAAW,OAAO,MAAM,GAAG,mBAC1B;kCACG,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,8OAAC,uBAAuB,CAAC,QAAQ,EAAE,OAAO;;;;;;;;;;;;YAOjD,CAAC,WAAW,WAAW,4BACtB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,SAAS;oBACT,WAAU;8BACX;;;;;;;;;;;YAOJ,CAAC,WAAW,CAAC,WAAW,OAAO,MAAM,GAAG,mBACvC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;;;;;;;AAO/C", "debugId": null}}, {"offset": {"line": 1049, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/release/adult-stream-app/src/lib/mockData.ts"], "sourcesContent": ["import { Video, Category, CategorySlug } from './types';\n\n// Helper function to create dates relative to now\nconst daysAgo = (days: number): Date => {\n  const date = new Date();\n  date.setDate(date.getDate() - days);\n  return date;\n};\n\n// Mock video data that matches the HTML structure\nexport const mockVideos: Video[] = [\n  {\n    id: '1',\n    title: 'Young woman in lingerie',\n    thumbnail: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDUy41muNMShuwIDWqjxpcQsV7KhLGyGjFY4zdqI7Lcv9QZT1oN4pWyVERS8-6L7o5y3C9GXEXy7FSJLA2KeZ0qQOZmBK9A4UgyD0CT61cpcZ5bKFbkydoy2pPARURUTf2WFHlzumJkGxLZ1R1FNrcqw-TQ9SmAaNA2EhR8CAppksKV8OmtYCDQ8J7erHloaYhJMPVYStNPL-K_buyV_qZz9utJBVUv7RUyV89suU8b4zyTDfRxaBopENbcMdAIcRAC5ytelvDvP2S4',\n    duration: 754, // 12:34\n    views: 1200000,\n    uploadedAt: daysAgo(2),\n    category: 'Amateur',\n    isHD: true,\n    isPremium: false\n  },\n  {\n    id: '2',\n    title: 'Blonde woman in lingerie',\n    thumbnail: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBUlCKajzjQ51XiZ1XJmkJn0Y9t1-BdUnT1khtxK_uCKPV0SaXL9jXdo6nhzOkWz1xxUIixENztti2MCFNZJ6t9ZeCmIWFbaD3CXQIEzxYYyeDDxtJQ7d-ilJ1dNPg-XKC_WzgP1wxSLTc2isQhvGvwj9UtqM4OgxYuH0cKyOfcRWt4IHEHFwF5B1JSoVp9Yna-CmWxsXeVBl8FBvJ_7htcaOOdzz2xL9WP5JHV0JvDA1-Mxezcf4BDrQhQDlKVlGTLgeWA8zRmztiF',\n    duration: 876, // 14:36\n    views: 876000,\n    uploadedAt: daysAgo(3),\n    category: 'Blonde',\n    isHD: true,\n    isPremium: true\n  },\n  {\n    id: '3',\n    title: 'Brunette woman in lingerie',\n    thumbnail: 'https://lh3.googleusercontent.com/aida-public/AB6AXuAEVaYijF6mrHoFDmO3Ou6JAyqc5srgGEsKgHh7BBycE9uDX8bLXJaET7CLsTUPtzOonp3DrbM4ktL_PgiNQC9rS6_XdUvblXWzZ9ibYzXNQWPdb9yzUqT7kOfKrAze_tOO5HwTsS2_FskSrZNOSJJdKr7mbq9TsdIABUv99KESPG2pMrckVGdcIQvzJ3zC9djcNW8IopWfiPic0IrnLhgBrD1aputtcv76dFdCGEcwIOIsKYFnRpYz9YKFGXQldrKk4lakhgFMv4kq',\n    duration: 1260, // 21:00\n    views: 2100000,\n    uploadedAt: daysAgo(1),\n    category: 'Brunette',\n    isHD: true,\n    isPremium: false\n  },\n  {\n    id: '4',\n    title: 'Redhead woman in lingerie',\n    thumbnail: 'https://lh3.googleusercontent.com/aida-public/AB6AXuA5UW7JBXy01u9nfj4vQ18qqtQPcUkusBi0l6R8YPHtm4Slp-wgwC7awH97TWOBVjhcmrhC1nVF3Y46nZ76B5mtnulzlXU4wxrOhMl7sYshljuBiFY1ZXbNbT-zQtJ62cd80iGwnnVvO7FtxjN072ZuUOBeqZhMwf4Wcmui29fNs9ikHX9Gh6kSixtcfzlQHk8OsnP0ofCgZMaMNqL8PqTVqGBB5Vdb6MkirIiDXrrCwmrzI4tc2_6p5y-Somi0JAdLr2CW-48pgZ8d',\n    duration: 990, // 16:30\n    views: 990000,\n    uploadedAt: daysAgo(5),\n    category: 'Redhead',\n    isHD: false,\n    isPremium: false\n  },\n  {\n    id: '5',\n    title: 'Asian woman in lingerie',\n    thumbnail: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDo0LKzHRIvjCB-31CLz0hnbWlCb3qVTKHgO7y3f1MsClO_3uI1JeykK-TSfVww2KfX9PbG7TCELgEf_cCdK1K9E5Z-Gs9z1AFz-toqoRMjwJaOqbyFntPUlQJXgwn1Yzs2YQWbauuSBi1hhqMmow08w1ujZ4I9inIcAi9rZ3NsDNaNCVFGLYVkDbxifUI-7OvHlzSNv8ew4YZZGE380MHB3kiYGFO8S6w2dZ9Y1n4L12xWeg7C8vij3Y630j5s4CdIYQvjYWCZxzq5',\n    duration: 900, // 15:00\n    views: 1500000,\n    uploadedAt: daysAgo(7),\n    category: 'Asian',\n    isHD: true,\n    isPremium: true\n  },\n  {\n    id: '6',\n    title: 'Black woman in lingerie',\n    thumbnail: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDC_SLMCvRT17RU2EgX8c86gxHNp0BsT7yDhIUycrt5ACfpynSejdT9qQm_1SikEwl9N0zgPG4WEjoX-PNZK1j72IoTr2fVZT7beEFUNQBQrLcrEeWzvzy5jqcDcQNBWCrpUeZiCGgIyWi-uL5G-UNC6JLBfH6MQTrlq0NVnBdsWBdEsgnPtmU7axWJsmsXlzKZNv3gLmPTD6JQ70OKwj8Sa7TGD8CYi9jnRjtO_Tr5WH6QiceQ1FweRMAY52UPHX0B4TG2zQqUlPVn',\n    duration: 750, // 12:30\n    views: 750000,\n    uploadedAt: daysAgo(4),\n    category: 'Black',\n    isHD: false,\n    isPremium: false\n  },\n  // Additional videos for variety\n  {\n    id: '7',\n    title: 'Passionate Encounter',\n    thumbnail: 'https://lh3.googleusercontent.com/aida-public/AB6AXuAPcsend9trbNMM90GBRpbSNmC_Kq9aH0deWS779inHD442q5xdeNSeC0noqpamPqNOxZC3YGnD2yq8_90OxRMCzFJn8fwjiYvN1f4qF9f6d2d17s-UHIIyESBlqyKGcqIw7zZJldFxS_GUjIa0gyrmhEAdLVfRgvlGCjwzM2jRwVaXzVaMFXSdeHScL8JFYRse8qNZDKeJnvuFtrPSGyoYsLpzmArcK5rsca_MQnB0x0JwLB4IAb3H1QJV-h3yeZZIL04HW8tEybPA',\n    duration: 754, // 12:34\n    views: 1800000,\n    uploadedAt: daysAgo(1),\n    category: 'MILF',\n    isHD: true,\n    isPremium: true\n  },\n  {\n    id: '8',\n    title: 'Late Night Rendezvous',\n    thumbnail: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBtC4vkPEiw7ywgTvH2AZG1PJmLJxjG7YWL2OpKvTBNebCHj6hsiLlQTsxOXo6zQQyoW9RpYRbSW8I2RWe2zmD4SE_lt4RPDZU7rJXBhy3547GNtn31kvytwHnqJGlHW18ZZYEe0dKt09sRV__cqP1IQ2w2Xj5kksCpbM6ZDnoBNs_CCr06TvaPBP2-ZXqz5mubDGcjE-rc54viVN7FhjlYcvjIkzQyJhpXBHL4CpxvRrG1Wh8nSxsQeToSPKfaoHZowjpNhWmsELTK',\n    duration: 922, // 15:22\n    views: 1200000,\n    uploadedAt: daysAgo(2),\n    category: 'POV',\n    isHD: true,\n    isPremium: false\n  },\n  {\n    id: '9',\n    title: 'Secret Affair',\n    thumbnail: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBXKzntxMMeqjyHfY_PuIioiUYpbxHbL_cSciXRstabbro_M4wOCbiMcUr8eRgS8KQ8SQgqxjGo3gbFgCYdxY9KrLLd3YYblQSdgdXzxAHhNEmuyqUHAgefSqh0fmobRQ8eE8gGf8cBkZLMy1BjzCZtBZUOOsRA7u9EKeqodDmezQS3t-YZYHjV9KcMxjvmZa6NUBiGsp1MreGCkSije65LJUAZeAadk6uPg6Mza_y166XxolTQDhaZ6bp5pKG5rNhVN0uLfMfq4DQ1',\n    duration: 611, // 10:11\n    views: 950000,\n    uploadedAt: daysAgo(3),\n    category: 'Teen',\n    isHD: false,\n    isPremium: false\n  },\n  {\n    id: '10',\n    title: 'Weekend Getaway',\n    thumbnail: 'https://lh3.googleusercontent.com/aida-public/AB6AXuAfbpcWlrcdbP8DitSf39CiCeaYzWgIGEQYriU4dKdkC5Jm06Jhf7I1IDETuOeIS3FFmdT7tszIL6koCC1mToMOxTX2nEIwDa_cpxd0erFP-082DRWV3W7igswVXHehuoPf-Em8LywA1_BaFMyPRimH4LsJvx57EnBYLLLE2zOMJizmz6SSJjipPfaNv9j2hgA5N6jPcfxJKEde9g0JFQEa0iRaUtK77JXSQZowSvpRNNEg3MMA0HozTQYVJuBs4gM8qbxe-ihbjNh_',\n    duration: 896, // 14:56\n    views: 1350000,\n    uploadedAt: daysAgo(6),\n    category: 'Mature',\n    isHD: true,\n    isPremium: true\n  },\n  {\n    id: '11',\n    title: 'Hidden Desires',\n    thumbnail: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBnLslL3Vern129flANNuPYvJjXNqoyAHgNL3kS-39AtH4bZ7QZOgxnQ9toFMuNUccssz8igWkMYgsnua6HTWebkmNEWU7ghJqv5HCuiZnitNVp-nlXUgnqBBElhiHpcuPjcw3KDVc83lSfJQqof89V8xQLBgeHy4kHTtTqzKfBrSOsfiEUcjUus4ykdZICOjhkO77CBqeVeS4EvXREdPgJk3sEdo1_EWu92n96_XULAQPJLBXYoe6mY5GPxZHo-7-JWUKfaiX_ltRf',\n    duration: 693, // 11:33\n    views: 780000,\n    uploadedAt: daysAgo(8),\n    category: 'Fetish',\n    isHD: false,\n    isPremium: false\n  },\n  {\n    id: '12',\n    title: 'Forbidden Love',\n    thumbnail: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBFarMdSUyVCDAi34TczrqS_o6ZtyjovxzA7YmbbKhkdecb8zvQuLbkVjre5pYnDwL7j9T02M-AdbaIKNQuPyJoljxdWUnj2A2KdAcZXAndbREuEg-lhTumHsK8ZOcljyCSawduMSpdgZEiqI_MC5B-J1X_uWhmrXaOeH1qXJ3o6O-i8nLuy_Ttf5Fpx5lbWm6qIPjCwKz5m_Uc0SyziUCiGMREBGkN9hj70SNl_SMUEXVDpJgyTuqKxennvyF5dcTGESou1Bh7P7Zz',\n    duration: 825, // 13:45\n    views: 1650000,\n    uploadedAt: daysAgo(5),\n    category: 'Lesbian',\n    isHD: true,\n    isPremium: true\n  }\n];\n\n// Category definitions matching the existing categories page\nexport const categories: Category[] = [\n  { name: 'Big Tits', slug: 'big-tits', image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDnVrvm1DwRW5Nfjjf3V27jy52hMYIGcco3ZlJtnPQO4XEmuPzX95VTiGpnpWNLgIDq-NBN8tfwwKs3GfJsNg9ykbfbVPEzy1sIbdTTFIQVLwM-Im9I40VWPNZMsjEqfLREM0ryz0zTxNKDEal-hBwuIoQtR73zy5F7Uu5gwwglcB9vZsoft22hhcTTCyliGOqmjenf6OFaiMEQyexn1cAsdeVyuO-u3Qjc5vyZKiOCq0tfDPB85WS36nkeiJ3H28nTJdyM82SEpV6x' },\n  { name: 'MILF', slug: 'milf', image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuCmjfZIDXtVPH_C-gs-SrE61OTbvUAGlwaJn6K3PV_CsY1hRwdMIKmdOZ4HKunOGhBtHrd-jH_mWiPJs-v0vmVH5WuiyoUiwNJ2QDTMYW6QXT19vVCPQX6A3l886Omv_CTKPtwIJe8Ajh7Hs_fxtXx5eVPRHQw7CdVbwHS4kjr5Fk28UGe7fCoZv4r01mnwsZJywEneQQIxww5VVyV6HP3ieQRGfCqwISJshl2fMdrrGxqpB3y1DUz448HIu5YNUryW8Zb9dWqydG5J' },\n  { name: 'POV', slug: 'pov', image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDtxp-r8-nct5ErDgiC3Y9K_7Lfh3wC4iFWqvaendssdIIdjbOY2I2oRMZp8Iq20v4zadIclbMQmoFWiHAM6VI9fZWUScgCbPWVVfOKEJc-OH33-q0RRYj2iHbttz5-rWCQ-4x4ZrNv4MS_QixUIAEMICXz5Q7dcBrq4OtyUiD9dCybkNsFKlK_jDKw2aeA0DbXSK2uT6QKm9FdyrWKJjfh-qoAr5DaezpTaVSuSPmbJ3we5FldeCnwRvmZpzFjBa8yvpe_xvjaZ3kY' },\n  { name: 'Blonde', slug: 'blonde', image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBWhSOS9vn6_9_6ejf1HFOULifKtjb-NFozoa7WleoS9BRTEpXTKgt3W9Ea1BQLTFjGaHHM_Rp0BT3RApvbKTBLWeJ0PQ2_D-8V3oKKiALVQvRjrjztPVbtLkIxx5E-RHhvftLVi2uWO0aO-auPqWFXQnf4DsjWToNQ_dnekwCbssmkuOi2vEW_FaWPSsIn-tIbvkKXCerIfqvXxMFSXuGbqIAS89EEgFiYgg96k0s1TEmm6TTqbRa0KCGzQ5xTqZUfjMMHsv0OKKnt' },\n  { name: 'Brunette', slug: 'brunette', image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDYi3vd_czZqaYivg094RWWAgCePpYzid85bqtivFxlQM8XBRFQeQkKHnC5fG-TkUjcW-n1oru_RVUyF2PMPMJ7fBP9SFXvM0dW2KY-Ub4ziOQncBtktrWQh_wmQrXxzZSCFGVCBa0WzlRtoKjgzW_AMEL48WkPvqfrjv0PG3_pQC8apwhp9H-TMDU2PTVANYxrzgaVe39zwICHYlVd7dgav9TQTGjs8X9AmOcRhInTShad2rpa3ysDQjhf3gnIp5-7BdjKNb1VgCcT' },\n  { name: 'Redhead', slug: 'redhead', image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuAwYqIvpZMNreOs2rxl7FDpHTo1jHTtaWMZ9xrhVQc7XEFsU_jldHjP7NjD682DYCKRM_zGH6PaLnHcjsS9GW-LOozqc6LU5cpu4eCU_6QtxdIaKSUyvK2n7jHjSVQhhEa4sFrf3-5dpcRUyYR-ZREoFvfFn2AqJZd4OzHckRVBQzzn5q6LdVAIz7Npb3CInyMgVAPcNN8e8QOruXo6m4qhCiivXfcjKxB7Ow0Z9qNfhsdXuvO62IBIzlD3-pL-0DLFmUb-Y9ZlDZrR' },\n  { name: 'Asian', slug: 'asian', image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBkLz9rJ6E4jNXu9ooPNepwPnZO_N5GzQ8mNZIGCPw_0ejGE4i1_AhOIQ9sLZT86No-qx8DN4EGXk8DGvjZJwhfIK0FanNu-UZ0MlOCPwbzJ-rme_WK5ombatxOXm9J9j2wYfMTAvp8AmehaSHige6iCyyFyb9JqjgW6P_BKWjnXePo80jhsTUW6ZxFcAnxug6CTr4HmPE824Aje2kF9fRyKjG2jZWyj0MPCKMx4x-pqg5YCZ89Mne66TGMwGGNe9YVsNg1hAEL_0h9' },\n  { name: 'Black', slug: 'black', image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuAKNscCiRR2AU5xuk7Sto_4jCpAWlAs8WVww2duHrGIZDciz9Vq6eUJ1T5xgUbzQ9ONv57gcI1LEIfjWCT-nQGxqTxkcdL3P6cQl6hYOmbEhEOimlLCaPLckHM2jnuvbVUjSuZbGUs15PUyefjz7t-bHnojwYp0TPDXah0FZwHWe0nhsmo2kEiC9fxmhzya3bQYRsLHMWw9v7Ugt4wCzdQXiclLdEbTEZ2s_odiZe-vj3pDG9MJ1gDCZ1l5ajMb3BN1c3L8ODsSLmqc' },\n  { name: 'Latina', slug: 'latina', image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuA7gY2rMtr_fSF1n2aeF1jWyPwpCas0JbNAeeZPrSQOcHxyZEjMs_U1yzSzHBzBMBq2vN4KKdcg2kujejDR2UAshHiTSdSiNJNB95FckaFVaU2wcH6BLqAG4u04KtAZY9R8ovvaHXftcVzxklRp8x2JgEJLvx37C6hIPZX0YZyg00sHsuOx65eOhHb9vz2XzeczA2oHR2lPc9yBTAfgxYF6JCBFLFdHgd-U6lbgugoL53Y85orncyjUvqRShrMRUnDwObdAH9lVrfNd' },\n  { name: 'Amateur', slug: 'amateur', image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBf2N2pLIYjyWHFQ6HGO97OAKvHNuktfcICn-4o6q2CRfU4D16K5tuwbrM7ySgZBMKnXlGWGOLGO0W4qLv-uNRhGKsdHvsvRQxBrACW4E152Lj3Dh-vykrTDm9Y7DtNwHptp3TyRQwqBKaZMSP4FmT99T2l1ay0SoQE24LT5Y85p3kpiF-o4-zQkHKoWFjQLAQctxRKgkJJcvhgUdR_pC3NEhRWwmHN_ogRpQA7tP5YagV0jr3hOE9XbAqaCPfZrTog0ihGFhfWRAba' },\n  { name: 'Teen', slug: 'teen', image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuA1DgnjUG2m78BS31Hh_p0_aEiNnUdgYeo3bAJvnDuadnvu1Mn-M7m9C84SIzavcd6jMIlC87tuCUvjAYE6s3kssrgx3vVWJafX1bzWjktOK9w4-dZ4fFuiELj7DzntR2T6L6AY3ak58qf5sTE0bRDUuS0ImhVGUAdYAT4JmImmwG8i9v7DCFj3_BtTneJdOHtGIDGt9M32j4L8vz65GXs1FAT6QG7Dem70WL-vPzr_GozEIW7H1dVr1shGYfJf5fHWCxO52znTuqk8' },\n  { name: 'Mature', slug: 'mature', image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuB399n5jsMLZP3E521_95YGyUj-JYCbAJRoMTXx8YoEhJ47CYdy-td9lfDhgXJjosQbXvV9QDYTroRoSP9m_vV4ipPQIUyRVcK8gsN4nF7KibY6SnI9Qc7AxKL3hXpe_o505GQAjhX0wOo1bOqwx3k4BUtuFervKeoA-VWP-h_JW2jASYcbsQrGVTvZbqr4WgqerKhHvtbbDUrunP_PvHheg9m1bxvp77iCQlk2iWLruLxfpdS0ymglBjANZfdtU4kPS8TXqKP39K3B' },\n  { name: 'Fetish', slug: 'fetish', image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBf-LU36CTb5r5ts_sQCH41lTXcfGFz1ml_iNqKQQBoD0BtBb9QzRH8lTMFBiZdbZM9xjchoTVx24hBy0KVfPqj5lIvHCk_Qd4-DDj-W6s3SnbwXXaM7BohcTIElYOb9VZjBtsE3PX7Sok1ha7Ni-k8iLrR1kzragF5DwKmmopPgsY6NoODvRe4oTYd_7b6Xt0vxuxPMclfwZ0Lem-4Ky2UAykKc6FyAjRrSJPfuj5ADZcXPY_-3momBEh8N9chZ56kFoI7SYKcY69V' },\n  { name: 'Gay', slug: 'gay', image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuAxwTW-zXUzUx_Usv5ZRAgn6gW6ZcJsbbXR8Id4piLm4wEy3F6eilSkZnvQp2ImmqDGzxm9mtUjnION_jxCuXsNsaGCPxLptAdJ8H6guWmzhpvnhPbSf-rRlriQE5np_bqdW3BWwzm6zyKd0UUTWMYzSusBsPRfAUqojCD-AD2vVUMzYHVCC3tJannfVnSvEg65Co94-2zMysoXEPE_ZB88-9wkH5jmVcOpyRsT27V0GdcbTd6nYHlx3BXWmnylM2O0BzX-iU6iruSi' },\n  { name: 'Lesbian', slug: 'lesbian', image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuA7xQu0FWYiw_vrF5lwSn5UgxhSRYcJyZWisEsOqVlUPPmGd9oaMeS-S3_zq7nit3La7ZrZn4bDf6v_8K2IVfmB9lwVs7UQM7salTbgRHRO-eO9DKbeulQTlw7xa0y_M_gN7RRIpuwO81xAa3VP6ijxcbWASQM4uR6TxUi8BMqjIsi1Oi12hv9JI0wGeuPjSLyJXzs4zHIQQTW3dF0DrPYI29qJG6gkOwnAjtEXpFYe2FO5y4rqfKg3EhXSMSycfbTk8-NbHNgC7eHe' },\n  { name: 'Transgender', slug: 'transgender', image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuA8YwPHRUZNPcf1Ab6FrvfXWLLoiTeLzD-GpIsP8PPCz0dXO6G6NSXWSxox11bzLt-gwDPJieIeUFv0ZgkfPrRf5Yc-8KtUUFdzr3P1W998eWxSpptQ3XuSPYJEh9eEmOOST_NZyEJRsDy45fYmwwpiYwkjjSxr19tsOIZiON5_nPU4dbSAMKchh6EJl1NXMZ7QjSkckQSVje5Gp-Ve1C0IUonXe6-e0tMX63W7BPEypLAQJWRl_PyQfs7saMhy_27BXnrhl40k1PQ4' }\n];\n\n// Helper functions for data manipulation\nexport function getCategoryBySlug(slug: string): Category | undefined {\n  return categories.find(cat => cat.slug === slug);\n}\n\nexport function getVideosByCategory(categorySlug: string): Video[] {\n  const category = getCategoryBySlug(categorySlug);\n  if (!category) return [];\n\n  // For now, return all videos. In a real app, this would filter by category\n  return mockVideos;\n}\n\nexport function sortVideosByRecent(videos: Video[]): Video[] {\n  return [...videos].sort((a, b) => b.uploadedAt.getTime() - a.uploadedAt.getTime());\n}\n\nexport function sortVideosByPopular(videos: Video[]): Video[] {\n  return [...videos].sort((a, b) => b.views - a.views);\n}\n\nexport function isValidCategorySlug(slug: string): slug is CategorySlug {\n  return categories.some(cat => cat.slug === slug);\n}\n"], "names": [], "mappings": ";;;;;;;;;AAEA,kDAAkD;AAClD,MAAM,UAAU,CAAC;IACf,MAAM,OAAO,IAAI;IACjB,KAAK,OAAO,CAAC,KAAK,OAAO,KAAK;IAC9B,OAAO;AACT;AAGO,MAAM,aAAsB;IACjC;QACE,IAAI;QACJ,OAAO;QACP,WAAW;QACX,UAAU;QACV,OAAO;QACP,YAAY,QAAQ;QACpB,UAAU;QACV,MAAM;QACN,WAAW;IACb;IACA;QACE,IAAI;QACJ,OAAO;QACP,WAAW;QACX,UAAU;QACV,OAAO;QACP,YAAY,QAAQ;QACpB,UAAU;QACV,MAAM;QACN,WAAW;IACb;IACA;QACE,IAAI;QACJ,OAAO;QACP,WAAW;QACX,UAAU;QACV,OAAO;QACP,YAAY,QAAQ;QACpB,UAAU;QACV,MAAM;QACN,WAAW;IACb;IACA;QACE,IAAI;QACJ,OAAO;QACP,WAAW;QACX,UAAU;QACV,OAAO;QACP,YAAY,QAAQ;QACpB,UAAU;QACV,MAAM;QACN,WAAW;IACb;IACA;QACE,IAAI;QACJ,OAAO;QACP,WAAW;QACX,UAAU;QACV,OAAO;QACP,YAAY,QAAQ;QACpB,UAAU;QACV,MAAM;QACN,WAAW;IACb;IACA;QACE,IAAI;QACJ,OAAO;QACP,WAAW;QACX,UAAU;QACV,OAAO;QACP,YAAY,QAAQ;QACpB,UAAU;QACV,MAAM;QACN,WAAW;IACb;IACA,gCAAgC;IAChC;QACE,IAAI;QACJ,OAAO;QACP,WAAW;QACX,UAAU;QACV,OAAO;QACP,YAAY,QAAQ;QACpB,UAAU;QACV,MAAM;QACN,WAAW;IACb;IACA;QACE,IAAI;QACJ,OAAO;QACP,WAAW;QACX,UAAU;QACV,OAAO;QACP,YAAY,QAAQ;QACpB,UAAU;QACV,MAAM;QACN,WAAW;IACb;IACA;QACE,IAAI;QACJ,OAAO;QACP,WAAW;QACX,UAAU;QACV,OAAO;QACP,YAAY,QAAQ;QACpB,UAAU;QACV,MAAM;QACN,WAAW;IACb;IACA;QACE,IAAI;QACJ,OAAO;QACP,WAAW;QACX,UAAU;QACV,OAAO;QACP,YAAY,QAAQ;QACpB,UAAU;QACV,MAAM;QACN,WAAW;IACb;IACA;QACE,IAAI;QACJ,OAAO;QACP,WAAW;QACX,UAAU;QACV,OAAO;QACP,YAAY,QAAQ;QACpB,UAAU;QACV,MAAM;QACN,WAAW;IACb;IACA;QACE,IAAI;QACJ,OAAO;QACP,WAAW;QACX,UAAU;QACV,OAAO;QACP,YAAY,QAAQ;QACpB,UAAU;QACV,MAAM;QACN,WAAW;IACb;CACD;AAGM,MAAM,aAAyB;IACpC;QAAE,MAAM;QAAY,MAAM;QAAY,OAAO;IAAqU;IAClX;QAAE,MAAM;QAAQ,MAAM;QAAQ,OAAO;IAAqU;IAC1W;QAAE,MAAM;QAAO,MAAM;QAAO,OAAO;IAAqU;IACxW;QAAE,MAAM;QAAU,MAAM;QAAU,OAAO;IAAqU;IAC9W;QAAE,MAAM;QAAY,MAAM;QAAY,OAAO;IAAqU;IAClX;QAAE,MAAM;QAAW,MAAM;QAAW,OAAO;IAAqU;IAChX;QAAE,MAAM;QAAS,MAAM;QAAS,OAAO;IAAqU;IAC5W;QAAE,MAAM;QAAS,MAAM;QAAS,OAAO;IAAqU;IAC5W;QAAE,MAAM;QAAU,MAAM;QAAU,OAAO;IAAqU;IAC9W;QAAE,MAAM;QAAW,MAAM;QAAW,OAAO;IAAqU;IAChX;QAAE,MAAM;QAAQ,MAAM;QAAQ,OAAO;IAAqU;IAC1W;QAAE,MAAM;QAAU,MAAM;QAAU,OAAO;IAAqU;IAC9W;QAAE,MAAM;QAAU,MAAM;QAAU,OAAO;IAAqU;IAC9W;QAAE,MAAM;QAAO,MAAM;QAAO,OAAO;IAAqU;IACxW;QAAE,MAAM;QAAW,MAAM;QAAW,OAAO;IAAqU;IAChX;QAAE,MAAM;QAAe,MAAM;QAAe,OAAO;IAAqU;CACzX;AAGM,SAAS,kBAAkB,IAAY;IAC5C,OAAO,WAAW,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK;AAC7C;AAEO,SAAS,oBAAoB,YAAoB;IACtD,MAAM,WAAW,kBAAkB;IACnC,IAAI,CAAC,UAAU,OAAO,EAAE;IAExB,2EAA2E;IAC3E,OAAO;AACT;AAEO,SAAS,mBAAmB,MAAe;IAChD,OAAO;WAAI;KAAO,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,UAAU,CAAC,OAAO,KAAK,EAAE,UAAU,CAAC,OAAO;AACjF;AAEO,SAAS,oBAAoB,MAAe;IACjD,OAAO;WAAI;KAAO,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AACrD;AAEO,SAAS,oBAAoB,IAAY;IAC9C,OAAO,WAAW,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK;AAC7C", "debugId": null}}, {"offset": {"line": 1309, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/release/adult-stream-app/src/hooks/useCategoryFilter.ts"], "sourcesContent": ["'use client';\n\nimport { useState, useMemo, useCallback } from 'react';\nimport { Video, SortOption } from '@/lib/types';\nimport { sortVideosByRecent, sortVideosByPopular } from '@/lib/mockData';\n\ninterface UseCategoryFilterProps {\n  videos: Video[];\n  initialSort?: SortOption;\n}\n\ninterface UseCategoryFilterReturn {\n  filteredVideos: Video[];\n  activeSort: SortOption;\n  setActiveSort: (sort: SortOption) => void;\n  isLoading: boolean;\n}\n\nexport function useCategoryFilter({ \n  videos, \n  initialSort = 'recent' \n}: UseCategoryFilterProps): UseCategoryFilterReturn {\n  const [activeSort, setActiveSort] = useState<SortOption>(initialSort);\n  const [isLoading, setIsLoading] = useState(false);\n\n  // Memoized filtered and sorted videos\n  const filteredVideos = useMemo(() => {\n    if (!videos || videos.length === 0) return [];\n\n    switch (activeSort) {\n      case 'recent':\n        return sortVideosByRecent(videos);\n      case 'popular':\n        return sortVideosByPopular(videos);\n      case 'duration':\n        return [...videos].sort((a, b) => b.duration - a.duration);\n      case 'views':\n        return [...videos].sort((a, b) => b.views - a.views);\n      default:\n        return videos;\n    }\n  }, [videos, activeSort]);\n\n  // Handle sort change with loading state\n  const handleSortChange = useCallback((sort: SortOption) => {\n    if (sort === activeSort) return;\n    \n    setIsLoading(true);\n    \n    // Simulate API delay for smooth UX\n    setTimeout(() => {\n      setActiveSort(sort);\n      setIsLoading(false);\n    }, 300);\n  }, [activeSort]);\n\n  return {\n    filteredVideos,\n    activeSort,\n    setActiveSort: handleSortChange,\n    isLoading\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;AAEA;AAJA;;;AAkBO,SAAS,kBAAkB,EAChC,MAAM,EACN,cAAc,QAAQ,EACC;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,sCAAsC;IACtC,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC7B,IAAI,CAAC,UAAU,OAAO,MAAM,KAAK,GAAG,OAAO,EAAE;QAE7C,OAAQ;YACN,KAAK;gBACH,OAAO,CAAA,GAAA,sHAAA,CAAA,qBAAkB,AAAD,EAAE;YAC5B,KAAK;gBACH,OAAO,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE;YAC7B,KAAK;gBACH,OAAO;uBAAI;iBAAO,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;YAC3D,KAAK;gBACH,OAAO;uBAAI;iBAAO,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;YACrD;gBACE,OAAO;QACX;IACF,GAAG;QAAC;QAAQ;KAAW;IAEvB,wCAAwC;IACxC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,IAAI,SAAS,YAAY;QAEzB,aAAa;QAEb,mCAAmC;QACnC,WAAW;YACT,cAAc;YACd,aAAa;QACf,GAAG;IACL,GAAG;QAAC;KAAW;IAEf,OAAO;QACL;QACA;QACA,eAAe;QACf;IACF;AACF", "debugId": null}}, {"offset": {"line": 1368, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/release/adult-stream-app/src/app/categories/%5Bcategory%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { notFound } from 'next/navigation';\nimport { CategoryLayout } from '@/components/category/CategoryLayout';\nimport { CategoryTitle } from '@/components/category/CategoryTitle';\nimport { CategoryTabs } from '@/components/category/CategoryTabs';\nimport { CategoryGrid } from '@/components/category/CategoryGrid';\nimport { useCategoryFilter } from '@/hooks/useCategoryFilter';\nimport { getCategoryBySlug, getVideosByCategory, isValidCategorySlug } from '@/lib/mockData';\n\ninterface CategoryPageProps {\n  params: Promise<{\n    category: string;\n  }>;\n}\n\nexport default async function CategoryPage({ params }: CategoryPageProps) {\n  const { category } = await params;\n\n  // Validate category parameter\n  if (!isValidCategorySlug(category)) {\n    notFound();\n  }\n\n  // Get category data\n  const categoryData = getCategoryBySlug(category);\n  if (!categoryData) {\n    notFound();\n  }\n\n  // Get videos for this category\n  const videos = getVideosByCategory(category);\n\n  // Use filtering hook\n  const { filteredVideos, activeSort, setActiveSort, isLoading } = useCategoryFilter({\n    videos,\n    initialSort: 'recent'\n  });\n\n  return (\n    <CategoryLayout>\n      {/* Category Title and Breadcrumb */}\n      <CategoryTitle\n        categoryName={categoryData.name}\n        videoCount={filteredVideos.length}\n      />\n\n      {/* Category Tabs */}\n      <CategoryTabs\n        activeTab={activeSort}\n        onTabChange={setActiveSort}\n      />\n\n      {/* Video Grid */}\n      <CategoryGrid\n        videos={filteredVideos}\n        loading={isLoading}\n        hasMore={false} // For now, no pagination\n      />\n    </CategoryLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;AAiBe,eAAe,aAAa,EAAE,MAAM,EAAqB;IACtE,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM;IAE3B,8BAA8B;IAC9B,IAAI,CAAC,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE,WAAW;QAClC,CAAA,GAAA,kIAAA,CAAA,WAAQ,AAAD;IACT;IAEA,oBAAoB;IACpB,MAAM,eAAe,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE;IACvC,IAAI,CAAC,cAAc;QACjB,CAAA,GAAA,kIAAA,CAAA,WAAQ,AAAD;IACT;IAEA,+BAA+B;IAC/B,MAAM,SAAS,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE;IAEnC,qBAAqB;IACrB,MAAM,EAAE,cAAc,EAAE,UAAU,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD,EAAE;QACjF;QACA,aAAa;IACf;IAEA,qBACE,8OAAC,gJAAA,CAAA,iBAAc;;0BAEb,8OAAC,+IAAA,CAAA,gBAAa;gBACZ,cAAc,aAAa,IAAI;gBAC/B,YAAY,eAAe,MAAM;;;;;;0BAInC,8OAAC,8IAAA,CAAA,eAAY;gBACX,WAAW;gBACX,aAAa;;;;;;0BAIf,8OAAC,8IAAA,CAAA,eAAY;gBACX,QAAQ;gBACR,SAAS;gBACT,SAAS;;;;;;;;;;;;AAIjB", "debugId": null}}]}