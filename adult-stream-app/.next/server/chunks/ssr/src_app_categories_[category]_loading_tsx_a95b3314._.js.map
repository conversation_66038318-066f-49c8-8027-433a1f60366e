{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/release/adult-stream-app/src/app/categories/%5Bcategory%5D/loading.tsx"], "sourcesContent": ["import React from 'react';\n\nexport default function CategoryLoading() {\n  return (\n    <div\n      className=\"relative flex size-full min-h-screen flex-col group/design-root overflow-x-hidden bg-[#1A0B0C] dark\"\n      style={{ fontFamily: '\"Plus Jakarta Sans\", \"Noto Sans\", sans-serif' }}\n    >\n      <div className=\"layout-container flex h-full grow flex-col\">\n        {/* Header skeleton */}\n        <header className=\"flex items-center justify-between whitespace-nowrap border-b border-solid border-[#391D1F] px-4 sm:px-6 lg:px-10 py-3\">\n          <div className=\"flex items-center gap-4 md:gap-8\">\n            <div className=\"flex items-center gap-2\">\n              <div className=\"h-6 w-6 bg-[#E92933] rounded animate-pulse\" />\n              <div className=\"h-6 w-20 bg-gray-700 rounded animate-pulse\" />\n            </div>\n          </div>\n          <div className=\"flex items-center gap-4\">\n            <div className=\"h-10 w-32 bg-gray-700 rounded animate-pulse\" />\n            <div className=\"h-10 w-10 bg-gray-700 rounded-full animate-pulse\" />\n          </div>\n        </header>\n\n        {/* Main content skeleton */}\n        <main className=\"flex flex-1 justify-center py-5 px-4 sm:px-6 lg:px-8\">\n          <div className=\"layout-content-container w-full max-w-screen-xl flex-col\">\n            {/* Title skeleton */}\n            <div className=\"flex flex-wrap items-center justify-between gap-4 p-4\">\n              <div className=\"h-8 w-48 bg-gray-700 rounded animate-pulse\" />\n            </div>\n\n            {/* Tabs skeleton */}\n            <div className=\"pb-3\">\n              <div className=\"flex border-b border-[#4A2528] px-4 gap-6 sm:gap-8\">\n                <div className=\"h-8 w-16 bg-gray-700 rounded animate-pulse\" />\n                <div className=\"h-8 w-20 bg-gray-700 rounded animate-pulse\" />\n              </div>\n            </div>\n\n            {/* Grid skeleton */}\n            <div className=\"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4 p-4\">\n              {Array.from({ length: 12 }).map((_, index) => (\n                <div key={index} className=\"flex flex-col gap-2\">\n                  {/* Video thumbnail skeleton */}\n                  <div className=\"aspect-video bg-gray-700 rounded-lg animate-pulse\" />\n                  {/* Title skeleton */}\n                  <div className=\"h-4 bg-gray-700 rounded animate-pulse\" />\n                  {/* Metadata skeleton */}\n                  <div className=\"h-3 w-3/4 bg-gray-600 rounded animate-pulse\" />\n                </div>\n              ))}\n            </div>\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QACC,WAAU;QACV,OAAO;YAAE,YAAY;QAA+C;kBAEpE,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAO,WAAU;;sCAChB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;sCAGnB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;8BAKnB,8OAAC;oBAAK,WAAU;8BACd,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;;;;;;;;;;0CAIjB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;0CAKnB,8OAAC;gCAAI,WAAU;0CACZ,MAAM,IAAI,CAAC;oCAAE,QAAQ;gCAAG,GAAG,GAAG,CAAC,CAAC,GAAG,sBAClC,8OAAC;wCAAgB,WAAU;;0DAEzB,8OAAC;gDAAI,WAAU;;;;;;0DAEf,8OAAC;gDAAI,WAAU;;;;;;0DAEf,8OAAC;gDAAI,WAAU;;;;;;;uCANP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAe1B", "debugId": null}}]}