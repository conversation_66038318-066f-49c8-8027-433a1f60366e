{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/release/adult-stream-app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatDuration(seconds: number): string {\n  const minutes = Math.floor(seconds / 60);\n  const remainingSeconds = seconds % 60;\n  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n}\n\nexport function formatViews(views: number): string {\n  if (views >= 1000000) {\n    return `${(views / 1000000).toFixed(1)}M`;\n  } else if (views >= 1000) {\n    return `${(views / 1000).toFixed(1)}K`;\n  }\n  return views.toString();\n}\n\nexport function formatTimeAgo(date: Date): string {\n  const now = new Date();\n  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n  \n  if (diffInSeconds < 60) {\n    return 'Just now';\n  } else if (diffInSeconds < 3600) {\n    const minutes = Math.floor(diffInSeconds / 60);\n    return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;\n  } else if (diffInSeconds < 86400) {\n    const hours = Math.floor(diffInSeconds / 3600);\n    return `${hours} hour${hours > 1 ? 's' : ''} ago`;\n  } else if (diffInSeconds < 604800) {\n    const days = Math.floor(diffInSeconds / 86400);\n    return `${days} day${days > 1 ? 's' : ''} ago`;\n  } else if (diffInSeconds < 2592000) {\n    const weeks = Math.floor(diffInSeconds / 604800);\n    return `${weeks} week${weeks > 1 ? 's' : ''} ago`;\n  } else if (diffInSeconds < 31536000) {\n    const months = Math.floor(diffInSeconds / 2592000);\n    return `${months} month${months > 1 ? 's' : ''} ago`;\n  } else {\n    const years = Math.floor(diffInSeconds / 31536000);\n    return `${years} year${years > 1 ? 's' : ''} ago`;\n  }\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,OAAe;IAC5C,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU;IACrC,MAAM,mBAAmB,UAAU;IACnC,OAAO,GAAG,QAAQ,CAAC,EAAE,iBAAiB,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;AACrE;AAEO,SAAS,YAAY,KAAa;IACvC,IAAI,SAAS,SAAS;QACpB,OAAO,GAAG,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IAC3C,OAAO,IAAI,SAAS,MAAM;QACxB,OAAO,GAAG,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IACxC;IACA,OAAO,MAAM,QAAQ;AACvB;AAEO,SAAS,cAAc,IAAU;IACtC,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI;IAEpE,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT,OAAO,IAAI,gBAAgB,MAAM;QAC/B,MAAM,UAAU,KAAK,KAAK,CAAC,gBAAgB;QAC3C,OAAO,GAAG,QAAQ,OAAO,EAAE,UAAU,IAAI,MAAM,GAAG,IAAI,CAAC;IACzD,OAAO,IAAI,gBAAgB,OAAO;QAChC,MAAM,QAAQ,KAAK,KAAK,CAAC,gBAAgB;QACzC,OAAO,GAAG,MAAM,KAAK,EAAE,QAAQ,IAAI,MAAM,GAAG,IAAI,CAAC;IACnD,OAAO,IAAI,gBAAgB,QAAQ;QACjC,MAAM,OAAO,KAAK,KAAK,CAAC,gBAAgB;QACxC,OAAO,GAAG,KAAK,IAAI,EAAE,OAAO,IAAI,MAAM,GAAG,IAAI,CAAC;IAChD,OAAO,IAAI,gBAAgB,SAAS;QAClC,MAAM,QAAQ,KAAK,KAAK,CAAC,gBAAgB;QACzC,OAAO,GAAG,MAAM,KAAK,EAAE,QAAQ,IAAI,MAAM,GAAG,IAAI,CAAC;IACnD,OAAO,IAAI,gBAAgB,UAAU;QACnC,MAAM,SAAS,KAAK,KAAK,CAAC,gBAAgB;QAC1C,OAAO,GAAG,OAAO,MAAM,EAAE,SAAS,IAAI,MAAM,GAAG,IAAI,CAAC;IACtD,OAAO;QACL,MAAM,QAAQ,KAAK,KAAK,CAAC,gBAAgB;QACzC,OAAO,GAAG,MAAM,KAAK,EAAE,QAAQ,IAAI,MAAM,GAAG,IAAI,CAAC;IACnD;AACF;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/release/adult-stream-app/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';\n  size?: 'sm' | 'md' | 'lg' | 'xl';\n  isLoading?: boolean;\n  children: React.ReactNode;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ \n    className, \n    variant = 'primary', \n    size = 'md', \n    isLoading = false, \n    disabled,\n    children, \n    ...props \n  }, ref) => {\n    const baseStyles = \"inline-flex items-center justify-center rounded-lg font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-background disabled:opacity-50 disabled:cursor-not-allowed\";\n    \n    const variants = {\n      primary: \"bg-primary text-white hover:bg-primary-hover focus:ring-primary\",\n      secondary: \"bg-background-input text-text hover:bg-background-hover focus:ring-background-input\",\n      outline: \"border border-border text-text hover:bg-background-input focus:ring-border\",\n      ghost: \"text-text hover:bg-background-input focus:ring-background-input\",\n      danger: \"bg-error text-white hover:bg-red-600 focus:ring-error\",\n    };\n    \n    const sizes = {\n      sm: \"h-8 px-3 text-sm\",\n      md: \"h-10 px-4 text-sm\",\n      lg: \"h-12 px-6 text-base\",\n      xl: \"h-14 px-8 text-lg\",\n    };\n\n    return (\n      <button\n        className={cn(\n          baseStyles,\n          variants[variant],\n          sizes[size],\n          isLoading && \"cursor-not-allowed\",\n          className\n        )}\n        disabled={disabled || isLoading}\n        ref={ref}\n        {...props}\n      >\n        {isLoading ? (\n          <>\n            <svg\n              className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n            >\n              <circle\n                className=\"opacity-25\"\n                cx=\"12\"\n                cy=\"12\"\n                r=\"10\"\n                stroke=\"currentColor\"\n                strokeWidth=\"4\"\n              ></circle>\n              <path\n                className=\"opacity-75\"\n                fill=\"currentColor\"\n                d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n              ></path>\n            </svg>\n            Loading...\n          </>\n        ) : (\n          children\n        )}\n      </button>\n    );\n  }\n);\n\nButton.displayName = \"Button\";\n\nexport { Button };\nexport type { ButtonProps };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC7B,CAAC,EACC,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,KAAK,EACjB,QAAQ,EACR,QAAQ,EACR,GAAG,OACJ,EAAE;IACD,MAAM,aAAa;IAEnB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,YACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX,aAAa,sBACb;QAEF,UAAU,YAAY;QACtB,KAAK;QACJ,GAAG,KAAK;kBAER,0BACC;;8BACE,6LAAC;oBACC,WAAU;oBACV,OAAM;oBACN,MAAK;oBACL,SAAQ;;sCAER,6LAAC;4BACC,WAAU;4BACV,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,QAAO;4BACP,aAAY;;;;;;sCAEd,6LAAC;4BACC,WAAU;4BACV,MAAK;4BACL,GAAE;;;;;;;;;;;;gBAEA;;2BAIR;;;;;;AAIR;;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/release/adult-stream-app/src/components/ui/SearchBar.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { cn } from '@/lib/utils';\nimport { debounce } from '@/lib/utils';\n\ninterface SearchBarProps {\n  className?: string;\n  placeholder?: string;\n  onSearch?: (query: string) => void;\n}\n\nexport function SearchBar({ \n  className, \n  placeholder = \"Search videos...\", \n  onSearch \n}: SearchBarProps) {\n  const [query, setQuery] = useState('');\n  const [isFocused, setIsFocused] = useState(false);\n  const [suggestions, setSuggestions] = useState<string[]>([]);\n  const [showSuggestions, setShowSuggestions] = useState(false);\n  const inputRef = useRef<HTMLInputElement>(null);\n  const router = useRouter();\n\n  // Mock suggestions - in a real app, this would come from an API\n  const mockSuggestions = [\n    'amateur',\n    'blonde',\n    'brunette',\n    'milf',\n    'teen',\n    'asian',\n    'latina',\n    'big tits',\n    'pov',\n    'hardcore',\n  ];\n\n  // Debounced search function\n  const debouncedSearch = debounce((searchQuery: string) => {\n    if (searchQuery.length > 0) {\n      const filtered = mockSuggestions.filter(suggestion =>\n        suggestion.toLowerCase().includes(searchQuery.toLowerCase())\n      );\n      setSuggestions(filtered.slice(0, 5));\n      setShowSuggestions(true);\n    } else {\n      setSuggestions([]);\n      setShowSuggestions(false);\n    }\n  }, 300);\n\n  useEffect(() => {\n    debouncedSearch(query);\n  }, [query, debouncedSearch]);\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (query.trim()) {\n      handleSearch(query.trim());\n    }\n  };\n\n  const handleSearch = (searchQuery: string) => {\n    setShowSuggestions(false);\n    if (onSearch) {\n      onSearch(searchQuery);\n    } else {\n      router.push(`/search?q=${encodeURIComponent(searchQuery)}`);\n    }\n  };\n\n  const handleSuggestionClick = (suggestion: string) => {\n    setQuery(suggestion);\n    handleSearch(suggestion);\n  };\n\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (e.key === 'Escape') {\n      setShowSuggestions(false);\n      inputRef.current?.blur();\n    }\n  };\n\n  return (\n    <div className={cn(\"relative flex-col min-w-32 !h-10 max-w-xs\", className)}>\n      <form onSubmit={handleSubmit} className=\"flex w-full flex-1 items-stretch rounded-lg h-full\">\n        {/* Search Icon */}\n        <div className=\"text-text-secondary flex border-none bg-background-input items-center justify-center pl-3 pr-2 rounded-l-lg border-r-0\">\n          <svg\n            fill=\"currentColor\"\n            height=\"20px\"\n            viewBox=\"0 0 256 256\"\n            width=\"20px\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n          >\n            <path d=\"M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z\" />\n          </svg>\n        </div>\n\n        {/* Input Field */}\n        <input\n          ref={inputRef}\n          type=\"text\"\n          value={query}\n          onChange={(e) => setQuery(e.target.value)}\n          onFocus={() => setIsFocused(true)}\n          onBlur={() => {\n            // Delay hiding suggestions to allow clicking on them\n            setTimeout(() => setShowSuggestions(false), 200);\n          }}\n          onKeyDown={handleKeyDown}\n          className={cn(\n            \"form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-r-lg text-text\",\n            \"focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary\",\n            \"border-none bg-background-input h-full placeholder:text-text-placeholder\",\n            \"px-3 text-sm font-normal leading-normal transition-colors\"\n          )}\n          placeholder={placeholder}\n        />\n      </form>\n\n      {/* Search Suggestions */}\n      {showSuggestions && suggestions.length > 0 && (\n        <div className=\"absolute top-full left-0 right-0 mt-1 bg-background-card border border-border rounded-lg shadow-lg z-50 max-h-60 overflow-y-auto\">\n          {suggestions.map((suggestion, index) => (\n            <button\n              key={index}\n              type=\"button\"\n              className=\"w-full text-left px-4 py-2 text-sm text-text hover:bg-background-hover transition-colors first:rounded-t-lg last:rounded-b-lg\"\n              onClick={() => handleSuggestionClick(suggestion)}\n            >\n              <span className=\"flex items-center gap-2\">\n                <svg\n                  className=\"w-4 h-4 text-text-secondary\"\n                  fill=\"currentColor\"\n                  viewBox=\"0 0 256 256\"\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                >\n                  <path d=\"M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z\" />\n                </svg>\n                {suggestion}\n              </span>\n            </button>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;;AAaO,SAAS,UAAU,EACxB,SAAS,EACT,cAAc,kBAAkB,EAChC,QAAQ,EACO;;IACf,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,gEAAgE;IAChE,MAAM,kBAAkB;QACtB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,4BAA4B;IAC5B,MAAM,kBAAkB,CAAA,GAAA,sHAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QAChC,IAAI,YAAY,MAAM,GAAG,GAAG;YAC1B,MAAM,WAAW,gBAAgB,MAAM,CAAC,CAAA,aACtC,WAAW,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;YAE3D,eAAe,SAAS,KAAK,CAAC,GAAG;YACjC,mBAAmB;QACrB,OAAO;YACL,eAAe,EAAE;YACjB,mBAAmB;QACrB;IACF,GAAG;IAEH,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,gBAAgB;QAClB;8BAAG;QAAC;QAAO;KAAgB;IAE3B,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,MAAM,IAAI,IAAI;YAChB,aAAa,MAAM,IAAI;QACzB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,mBAAmB;QACnB,IAAI,UAAU;YACZ,SAAS;QACX,OAAO;YACL,OAAO,IAAI,CAAC,CAAC,UAAU,EAAE,mBAAmB,cAAc;QAC5D;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,SAAS;QACT,aAAa;IACf;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,UAAU;YACtB,mBAAmB;YACnB,SAAS,OAAO,EAAE;QACpB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;;0BAC9D,6LAAC;gBAAK,UAAU;gBAAc,WAAU;;kCAEtC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,MAAK;4BACL,QAAO;4BACP,SAAQ;4BACR,OAAM;4BACN,OAAM;sCAEN,cAAA,6LAAC;gCAAK,GAAE;;;;;;;;;;;;;;;;kCAKZ,6LAAC;wBACC,KAAK;wBACL,MAAK;wBACL,OAAO;wBACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wBACxC,SAAS,IAAM,aAAa;wBAC5B,QAAQ;4BACN,qDAAqD;4BACrD,WAAW,IAAM,mBAAmB,QAAQ;wBAC9C;wBACA,WAAW;wBACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4FACA,2EACA,4EACA;wBAEF,aAAa;;;;;;;;;;;;YAKhB,mBAAmB,YAAY,MAAM,GAAG,mBACvC,6LAAC;gBAAI,WAAU;0BACZ,YAAY,GAAG,CAAC,CAAC,YAAY,sBAC5B,6LAAC;wBAEC,MAAK;wBACL,WAAU;wBACV,SAAS,IAAM,sBAAsB;kCAErC,cAAA,6LAAC;4BAAK,WAAU;;8CACd,6LAAC;oCACC,WAAU;oCACV,MAAK;oCACL,SAAQ;oCACR,OAAM;8CAEN,cAAA,6LAAC;wCAAK,GAAE;;;;;;;;;;;gCAET;;;;;;;uBAdE;;;;;;;;;;;;;;;;AAsBnB;GAzIgB;;QAUC,qIAAA,CAAA,YAAS;;;KAVV", "debugId": null}}, {"offset": {"line": 375, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/release/adult-stream-app/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { Button } from '@/components/ui/Button';\nimport { SearchBar } from '@/components/ui/SearchBar';\nimport { cn } from '@/lib/utils';\n\ninterface HeaderProps {\n  className?: string;\n}\n\nexport function Header({ className }: HeaderProps) {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  const navigation = [\n    { name: 'Home', href: '/' },\n    { name: 'Videos', href: '/videos' },\n    { name: 'Categories', href: '/categories' },\n    { name: 'Models', href: '/models' },\n    { name: 'Live', href: '/live' },\n    { name: 'Premium', href: '/premium' },\n  ];\n\n  return (\n    <header className={cn(\n      \"flex items-center justify-between whitespace-nowrap border-b border-solid border-border px-6 md:px-10 py-4\",\n      className\n    )}>\n      {/* Logo and Navigation */}\n      <div className=\"flex items-center gap-6 md:gap-8\">\n        {/* Logo */}\n        <Link href=\"/\" className=\"flex items-center gap-2 text-text\">\n          <div className=\"size-5 md:size-6 text-primary\">\n            <svg fill=\"none\" viewBox=\"0 0 48 48\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path\n                d=\"M24 4C25.7818 14.2173 33.7827 22.2182 44 24C33.7827 25.7818 25.7818 33.7827 24 44C22.2182 33.7827 14.2173 25.7818 4 24C14.2173 22.2182 22.2182 14.2173 24 4Z\"\n                fill=\"currentColor\"\n              />\n            </svg>\n          </div>\n          <h1 className=\"text-text text-xl md:text-2xl font-bold tracking-tight\">\n            AdultStream\n          </h1>\n        </Link>\n\n        {/* Desktop Navigation */}\n        <nav className=\"hidden md:flex items-center gap-6\">\n          {navigation.map((item) => (\n            <Link\n              key={item.name}\n              href={item.href}\n              className=\"text-text hover:text-primary text-sm font-semibold leading-normal transition-colors\"\n            >\n              {item.name}\n            </Link>\n          ))}\n        </nav>\n      </div>\n\n      {/* Right Side Actions */}\n      <div className=\"flex flex-1 justify-end items-center gap-3 md:gap-4\">\n        {/* Search Bar - Hidden on mobile */}\n        <div className=\"hidden sm:block\">\n          <SearchBar />\n        </div>\n\n        {/* Notifications */}\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          className=\"flex items-center justify-center rounded-lg h-10 w-10 bg-background-input hover:bg-background-hover text-text transition-colors\"\n          aria-label=\"Notifications\"\n        >\n          <svg\n            fill=\"currentColor\"\n            height=\"20px\"\n            viewBox=\"0 0 256 256\"\n            width=\"20px\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n          >\n            <path d=\"M221.8,175.94C216.25,166.38,208,139.33,208,104a80,80,0,1,0-160,0c0,35.34-8.26,62.38-13.81,71.94A16,16,0,0,0,48,200H88.81a40,40,0,0,0,78.38,0H208a16,16,0,0,0,13.8-24.06ZM128,216a24,24,0,0,1-22.62-16h45.24A24,24,0,0,1,128,216ZM48,184c7.7-13.24,16-43.92,16-80a64,64,0,1,1,128,0c0,36.05,8.28,66.73,16,80Z\" />\n          </svg>\n        </Button>\n\n        {/* User Avatar */}\n        <div\n          className=\"bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10 border-2 border-primary cursor-pointer\"\n          style={{\n            backgroundImage: `url(\"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face\")`,\n          }}\n        />\n\n        {/* Mobile Menu Button */}\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          className=\"md:hidden text-text hover:text-primary p-2\"\n          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n          aria-label=\"Toggle mobile menu\"\n        >\n          <svg\n            fill=\"currentColor\"\n            height=\"24px\"\n            viewBox=\"0 0 256 256\"\n            width=\"24px\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n          >\n            <path d=\"M224,128a8,8,0,0,1-8,8H40a8,8,0,0,1,0-16H216A8,8,0,0,1,224,128ZM40,88H216a8,8,0,0,0,0-16H40a8,8,0,0,0,0,16ZM40,184H216a8,8,0,0,0,0-16H40a8,8,0,0,0,0,16Z\" />\n          </svg>\n        </Button>\n      </div>\n\n      {/* Mobile Menu */}\n      {isMobileMenuOpen && (\n        <div className=\"absolute top-full left-0 right-0 bg-background-card border-b border-border md:hidden z-50\">\n          <div className=\"px-6 py-4 space-y-4\">\n            {/* Mobile Search */}\n            <SearchBar />\n            \n            {/* Mobile Navigation */}\n            <nav className=\"space-y-2\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"block text-text hover:text-primary text-sm font-semibold leading-normal transition-colors py-2\"\n                  onClick={() => setIsMobileMenuOpen(false)}\n                >\n                  {item.name}\n                </Link>\n              ))}\n            </nav>\n          </div>\n        </div>\n      )}\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAYO,SAAS,OAAO,EAAE,SAAS,EAAe;;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,aAAa;QACjB;YAAE,MAAM;YAAQ,MAAM;QAAI;QAC1B;YAAE,MAAM;YAAU,MAAM;QAAU;QAClC;YAAE,MAAM;YAAc,MAAM;QAAc;QAC1C;YAAE,MAAM;YAAU,MAAM;QAAU;QAClC;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IAED,qBACE,6LAAC;QAAO,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAClB,8GACA;;0BAGA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAU;;0CACvB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,MAAK;oCAAO,SAAQ;oCAAY,OAAM;8CACzC,cAAA,6LAAC;wCACC,GAAE;wCACF,MAAK;;;;;;;;;;;;;;;;0CAIX,6LAAC;gCAAG,WAAU;0CAAyD;;;;;;;;;;;;kCAMzE,6LAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAU;0CAET,KAAK,IAAI;+BAJL,KAAK,IAAI;;;;;;;;;;;;;;;;0BAWtB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,wIAAA,CAAA,YAAS;;;;;;;;;;kCAIZ,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,cAAW;kCAEX,cAAA,6LAAC;4BACC,MAAK;4BACL,QAAO;4BACP,SAAQ;4BACR,OAAM;4BACN,OAAM;sCAEN,cAAA,6LAAC;gCAAK,GAAE;;;;;;;;;;;;;;;;kCAKZ,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,iBAAiB,CAAC,gGAAgG,CAAC;wBACrH;;;;;;kCAIF,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS,IAAM,oBAAoB,CAAC;wBACpC,cAAW;kCAEX,cAAA,6LAAC;4BACC,MAAK;4BACL,QAAO;4BACP,SAAQ;4BACR,OAAM;4BACN,OAAM;sCAEN,cAAA,6LAAC;gCAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;YAMb,kCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,wIAAA,CAAA,YAAS;;;;;sCAGV,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,oBAAoB;8CAElC,KAAK,IAAI;mCALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;AAchC;GA9HgB;KAAA", "debugId": null}}, {"offset": {"line": 637, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/release/adult-stream-app/src/components/layout/Footer.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport { cn } from '@/lib/utils';\n\ninterface FooterProps {\n  className?: string;\n}\n\nexport function Footer({ className }: FooterProps) {\n  const currentYear = new Date().getFullYear();\n\n  const footerLinks = [\n    { name: 'Terms of Service', href: '/terms' },\n    { name: 'Privacy Policy', href: '/privacy' },\n    { name: 'Contact Us', href: '/contact' },\n    { name: 'Help', href: '/help' },\n    { name: 'DMCA', href: '/dmca' },\n  ];\n\n  const socialLinks = [\n    {\n      name: 'Twitter',\n      href: '#',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n          <path d=\"M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z\" />\n        </svg>\n      ),\n    },\n    {\n      name: 'Instagram',\n      href: '#',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n          <path d=\"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987s11.987-5.367 11.987-11.987C24.004 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.807.875 1.297 2.026 1.297 3.323s-.49 2.448-1.297 3.323c-.875.807-2.026 1.297-3.323 1.297zm7.718-1.297c-.875.807-2.026 1.297-3.323 1.297s-2.448-.49-3.323-1.297c-.807-.875-1.297-2.026-1.297-3.323s.49-2.448 1.297-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.807.875 1.297 2.026 1.297 3.323s-.49 2.448-1.297 3.323z\" />\n        </svg>\n      ),\n    },\n    {\n      name: 'Discord',\n      href: '#',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n          <path d=\"M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419-.0189 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9555 2.4189-2.1568 2.4189Z\" />\n        </svg>\n      ),\n    },\n  ];\n\n  return (\n    <footer className={cn(\n      \"border-t border-border py-8 px-6 md:px-10 bg-background-secondary\",\n      className\n    )}>\n      <div className=\"max-w-7xl mx-auto\">\n\n\n        {/* Age Verification Notice */}\n        <div className=\"bg-background-input rounded-lg p-4 mb-6\">\n          <div className=\"flex items-start gap-3\">\n            <div className=\"text-warning flex-shrink-0 mt-0.5\">\n              <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\n              </svg>\n            </div>\n            <div>\n              <h4 className=\"text-text font-semibold text-sm mb-1\">Age Verification Required</h4>\n              <p className=\"text-text-secondary text-xs leading-relaxed\">\n                This website contains adult content. By accessing this site, you confirm that you are 18 years or older\n                and agree to our terms of service. All models are 18+ years old.\n              </p>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Section */}\n        <div className=\"flex flex-col md:flex-row justify-between items-center pt-6 border-t border-border\">\n          {/* Copyright */}\n          <p className=\"text-text-secondary text-xs mb-4 md:mb-0\">\n            © {currentYear} All rights reserved.\n          </p>\n\n          {/* Legal Links */}\n          <div className=\"flex flex-wrap items-center gap-4 mb-4 md:mb-0\">\n            {footerLinks.map((link, index) => (\n              <React.Fragment key={link.name}>\n                <Link\n                  href={link.href}\n                  className=\"text-text-muted hover:text-text text-xs transition-colors\"\n                >\n                  {link.name}\n                </Link>\n                {index < footerLinks.length - 1 && (\n                  <span className=\"text-text-muted text-xs\">•</span>\n                )}\n              </React.Fragment>\n            ))}\n          </div>\n\n          {/* Social Links */}\n          <div className=\"flex items-center gap-3\">\n            {socialLinks.map((social) => (\n              <Link\n                key={social.name}\n                href={social.href}\n                className=\"text-text-secondary hover:text-primary transition-colors\"\n                aria-label={social.name}\n              >\n                {social.icon}\n              </Link>\n            ))}\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAMO,SAAS,OAAO,EAAE,SAAS,EAAe;IAC/C,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,cAAc;QAClB;YAAE,MAAM;YAAoB,MAAM;QAAS;QAC3C;YAAE,MAAM;YAAkB,MAAM;QAAW;QAC3C;YAAE,MAAM;YAAc,MAAM;QAAW;QACvC;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAQ,MAAM;QAAQ;KAC/B;IAED,MAAM,cAAc;QAClB;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAe,SAAQ;gBAAY,OAAM;0BACrE,cAAA,6LAAC;oBAAK,GAAE;;;;;;;;;;;QAGd;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAe,SAAQ;gBAAY,OAAM;0BACrE,cAAA,6LAAC;oBAAK,GAAE;;;;;;;;;;;QAGd;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAe,SAAQ;gBAAY,OAAM;0BACrE,cAAA,6LAAC;oBAAK,GAAE;;;;;;;;;;;QAGd;KACD;IAED,qBACE,6LAAC;QAAO,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAClB,qEACA;kBAEA,cAAA,6LAAC;YAAI,WAAU;;8BAIb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAe,SAAQ;8CACnD,cAAA,6LAAC;wCAAK,GAAE;;;;;;;;;;;;;;;;0CAGZ,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAuC;;;;;;kDACrD,6LAAC;wCAAE,WAAU;kDAA8C;;;;;;;;;;;;;;;;;;;;;;;8BASjE,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAE,WAAU;;gCAA2C;gCACnD;gCAAY;;;;;;;sCAIjB,6LAAC;4BAAI,WAAU;sCACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,6LAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAU;sDAET,KAAK,IAAI;;;;;;wCAEX,QAAQ,YAAY,MAAM,GAAG,mBAC5B,6LAAC;4CAAK,WAAU;sDAA0B;;;;;;;mCARzB,KAAK,IAAI;;;;;;;;;;sCAelC,6LAAC;4BAAI,WAAU;sCACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,OAAO,IAAI;oCACjB,WAAU;oCACV,cAAY,OAAO,IAAI;8CAEtB,OAAO,IAAI;mCALP,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;AAahC;KA5GgB", "debugId": null}}, {"offset": {"line": 899, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/release/adult-stream-app/src/components/layout/MainLayout.tsx"], "sourcesContent": ["import React from 'react';\nimport { Head<PERSON> } from './Header';\nimport { Footer } from './Footer';\nimport { cn } from '@/lib/utils';\n\ninterface MainLayoutProps {\n  children: React.ReactNode;\n  className?: string;\n  showHeader?: boolean;\n  showFooter?: boolean;\n}\n\nexport function MainLayout({ \n  children, \n  className,\n  showHeader = true,\n  showFooter = true \n}: MainLayoutProps) {\n  return (\n    <div className={cn(\n      \"relative flex size-full min-h-screen flex-col bg-background group/design-root overflow-x-hidden\",\n      className\n    )}>\n      <div className=\"layout-container flex h-full grow flex-col\">\n        {showHeader && <Header />}\n        \n        <main className=\"flex flex-1 flex-col\">\n          {children}\n        </main>\n        \n        {showFooter && <Footer />}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AASO,SAAS,WAAW,EACzB,QAAQ,EACR,SAAS,EACT,aAAa,IAAI,EACjB,aAAa,IAAI,EACD;IAChB,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,mGACA;kBAEA,cAAA,6LAAC;YAAI,WAAU;;gBACZ,4BAAc,6LAAC,yIAAA,CAAA,SAAM;;;;;8BAEtB,6LAAC;oBAAK,WAAU;8BACb;;;;;;gBAGF,4BAAc,6LAAC,yIAAA,CAAA,SAAM;;;;;;;;;;;;;;;;AAI9B;KAtBgB", "debugId": null}}, {"offset": {"line": 958, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/release/adult-stream-app/src/components/ui/VideoCard.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { cn, formatDuration, formatViews, formatTimeAgo } from '@/lib/utils';\n\ninterface VideoCardProps {\n  id: string;\n  title: string;\n  thumbnail: string;\n  duration: number; // in seconds\n  views?: number;\n  uploadedAt?: Date;\n  category?: string;\n  isHD?: boolean;\n  isPremium?: boolean;\n  className?: string;\n  onClick?: () => void;\n}\n\nexport function VideoCard({\n  id,\n  title,\n  thumbnail,\n  duration,\n  views,\n  uploadedAt,\n  category,\n  isHD = false,\n  isPremium = false,\n  className,\n  onClick,\n}: VideoCardProps) {\n  const handleClick = () => {\n    if (onClick) {\n      onClick();\n    }\n  };\n\n  const cardContent = (\n    <div className={cn(\"flex flex-col gap-2 group cursor-pointer\", className)}>\n      {/* Thumbnail Container */}\n      <div className=\"relative w-full aspect-video rounded-lg overflow-hidden shadow-lg group-hover:shadow-primary/30 transition-shadow\">\n        {/* Thumbnail Image */}\n        <div className=\"relative w-full h-full\">\n          <Image\n            src={thumbnail}\n            alt={title}\n            fill\n            className=\"object-cover transition-transform duration-300 group-hover:scale-105\"\n            sizes=\"(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, (max-width: 1280px) 25vw, 20vw\"\n          />\n          \n          {/* Overlay Gradient */}\n          <div className=\"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n        </div>\n\n        {/* Duration Badge */}\n        <div className=\"video-thumbnail-overlay\">\n          {formatDuration(duration)}\n        </div>\n\n        {/* Quality Badges */}\n        <div className=\"absolute top-2 left-2 flex gap-1\">\n          {isHD && (\n            <span className=\"bg-primary text-white text-xs font-bold px-1.5 py-0.5 rounded\">\n              HD\n            </span>\n          )}\n          {isPremium && (\n            <span className=\"bg-warning text-black text-xs font-bold px-1.5 py-0.5 rounded\">\n              PREMIUM\n            </span>\n          )}\n        </div>\n\n        {/* Play Button Overlay */}\n        <div className=\"video-thumbnail-overlay-play\">\n          <svg\n            fill=\"white\"\n            height=\"32\"\n            viewBox=\"0 0 256 256\"\n            width=\"32\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n          >\n            <path d=\"M240,128a15.74,15.74,0,0,1-7.6,13.51L88.32,229.65a16,16,0,0,1-24.32-13.51V40A16,16,0,0,1,88.32,26.35l144.08,88.14A15.74,15.74,0,0,1,240,128Z\" />\n          </svg>\n        </div>\n\n        {/* Hover Actions */}\n        <div className=\"absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n          <div className=\"flex gap-1\">\n            {/* Favorite Button */}\n            <button\n              className=\"bg-black/60 hover:bg-black/80 text-white p-1.5 rounded-full transition-colors\"\n              onClick={(e) => {\n                e.preventDefault();\n                e.stopPropagation();\n                // Handle favorite action\n              }}\n              aria-label=\"Add to favorites\"\n            >\n              <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path d=\"M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z\"/>\n              </svg>\n            </button>\n\n            {/* Watch Later Button */}\n            <button\n              className=\"bg-black/60 hover:bg-black/80 text-white p-1.5 rounded-full transition-colors\"\n              onClick={(e) => {\n                e.preventDefault();\n                e.stopPropagation();\n                // Handle watch later action\n              }}\n              aria-label=\"Watch later\"\n            >\n              <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\n              </svg>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Video Info */}\n      <div className=\"space-y-1\">\n        {/* Title */}\n        <h3 className=\"text-text text-sm font-semibold leading-tight line-clamp-2 group-hover:text-primary transition-colors\">\n          {title}\n        </h3>\n\n        {/* Metadata */}\n        <div className=\"flex items-center justify-between text-xs\">\n          <div className=\"flex items-center gap-2 text-text-muted\">\n            {views && (\n              <span>{formatViews(views)} views</span>\n            )}\n            {views && uploadedAt && (\n              <span>•</span>\n            )}\n            {uploadedAt && (\n              <span>{formatTimeAgo(uploadedAt)}</span>\n            )}\n          </div>\n          \n          {category && (\n            <span className=\"text-text-secondary text-xs bg-background-input px-2 py-0.5 rounded\">\n              {category}\n            </span>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n\n  if (onClick) {\n    return (\n      <div onClick={handleClick}>\n        {cardContent}\n      </div>\n    );\n  }\n\n  return (\n    <Link href={`/video/${id}`} className=\"block\">\n      {cardContent}\n    </Link>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AAgBO,SAAS,UAAU,EACxB,EAAE,EACF,KAAK,EACL,SAAS,EACT,QAAQ,EACR,KAAK,EACL,UAAU,EACV,QAAQ,EACR,OAAO,KAAK,EACZ,YAAY,KAAK,EACjB,SAAS,EACT,OAAO,EACQ;IACf,MAAM,cAAc;QAClB,IAAI,SAAS;YACX;QACF;IACF;IAEA,MAAM,4BACJ,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;;0BAE7D,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAK;gCACL,KAAK;gCACL,IAAI;gCACJ,WAAU;gCACV,OAAM;;;;;;0CAIR,6LAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,6LAAC;wBAAI,WAAU;kCACZ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;kCAIlB,6LAAC;wBAAI,WAAU;;4BACZ,sBACC,6LAAC;gCAAK,WAAU;0CAAgE;;;;;;4BAIjF,2BACC,6LAAC;gCAAK,WAAU;0CAAgE;;;;;;;;;;;;kCAOpF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,MAAK;4BACL,QAAO;4BACP,SAAQ;4BACR,OAAM;4BACN,OAAM;sCAEN,cAAA,6LAAC;gCAAK,GAAE;;;;;;;;;;;;;;;;kCAKZ,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCACC,WAAU;oCACV,SAAS,CAAC;wCACR,EAAE,cAAc;wCAChB,EAAE,eAAe;oCACjB,yBAAyB;oCAC3B;oCACA,cAAW;8CAEX,cAAA,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAe,SAAQ;kDACnD,cAAA,6LAAC;4CAAK,GAAE;;;;;;;;;;;;;;;;8CAKZ,6LAAC;oCACC,WAAU;oCACV,SAAS,CAAC;wCACR,EAAE,cAAc;wCAChB,EAAE,eAAe;oCACjB,4BAA4B;oCAC9B;oCACA,cAAW;8CAEX,cAAA,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAe,SAAQ;kDACnD,cAAA,6LAAC;4CAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQlB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAG,WAAU;kCACX;;;;;;kCAIH,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;oCACZ,uBACC,6LAAC;;4CAAM,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE;4CAAO;;;;;;;oCAE3B,SAAS,4BACR,6LAAC;kDAAK;;;;;;oCAEP,4BACC,6LAAC;kDAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE;;;;;;;;;;;;4BAIxB,0BACC,6LAAC;gCAAK,WAAU;0CACb;;;;;;;;;;;;;;;;;;;;;;;;IAQb,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,SAAS;sBACX;;;;;;IAGP;IAEA,qBACE,6LAAC,+JAAA,CAAA,UAAI;QAAC,MAAM,CAAC,OAAO,EAAE,IAAI;QAAE,WAAU;kBACnC;;;;;;AAGP;KArJgB", "debugId": null}}, {"offset": {"line": 1251, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/release/adult-stream-app/src/app/video/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { useParams } from 'next/navigation';\nimport { MainLayout } from '@/components/layout/MainLayout';\nimport { VideoCard } from '@/components/ui/VideoCard';\nimport { Button } from '@/components/ui/Button';\nimport { formatViews, formatTimeAgo } from '@/lib/utils';\n\n// Mock video data\nconst mockVideo = {\n  id: '1',\n  title: 'Passionate Encounter - Full HD Experience',\n  description: 'Experience the ultimate in romantic passion with this stunning HD video featuring beautiful performers in an intimate setting. This premium content showcases the art of love-making with exceptional cinematography and authentic chemistry.',\n  duration: 754, // 12:34\n  views: 125000,\n  likes: 8934,\n  dislikes: 234,\n  uploadedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),\n  category: 'Romance',\n  tags: ['passionate', 'romantic', 'hd', 'intimate', 'couple'],\n  isHD: true,\n  isPremium: false,\n  videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',\n  thumbnail: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800&h=450&fit=crop',\n  uploader: {\n    name: 'PremiumStudios',\n    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face',\n    verified: true,\n    subscribers: 45600,\n  }\n};\n\n// Mock related videos\nconst relatedVideos = [\n  {\n    id: '2',\n    title: 'Late Night Rendezvous',\n    thumbnail: 'https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?w=400&h=225&fit=crop',\n    duration: 922,\n    views: 89000,\n    uploadedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),\n    category: 'Drama',\n    isPremium: true,\n  },\n  {\n    id: '3',\n    title: 'Secret Affair',\n    thumbnail: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=225&fit=crop',\n    duration: 611,\n    views: 234000,\n    uploadedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),\n    category: 'Thriller',\n    isHD: true,\n  },\n  {\n    id: '4',\n    title: 'Weekend Getaway',\n    thumbnail: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=400&h=225&fit=crop',\n    duration: 896,\n    views: 156000,\n    uploadedAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),\n    category: 'Adventure',\n  },\n];\n\nexport default function VideoPlayerPage() {\n  const params = useParams();\n  const [isLiked, setIsLiked] = useState(false);\n  const [isDisliked, setIsDisliked] = useState(false);\n  const [isFavorited, setIsFavorited] = useState(false);\n  const [showDescription, setShowDescription] = useState(false);\n\n  const handleLike = () => {\n    setIsLiked(!isLiked);\n    if (isDisliked) setIsDisliked(false);\n  };\n\n  const handleDislike = () => {\n    setIsDisliked(!isDisliked);\n    if (isLiked) setIsLiked(false);\n  };\n\n  return (\n    <MainLayout>\n      <div className=\"px-4 sm:px-6 md:px-10 lg:px-16 xl:px-24 2xl:px-40 flex flex-1 justify-center py-6\">\n        <div className=\"layout-content-container flex flex-col lg:flex-row w-full max-w-screen-2xl gap-8\">\n          {/* Main Video Section */}\n          <div className=\"flex-1\">\n            {/* Video Player */}\n            <div className=\"relative w-full aspect-video bg-black rounded-lg overflow-hidden shadow-2xl mb-6\">\n              <video\n                className=\"w-full h-full object-cover\"\n                controls\n                poster={mockVideo.thumbnail}\n                preload=\"metadata\"\n              >\n                <source src={mockVideo.videoUrl} type=\"video/mp4\" />\n                Your browser does not support the video tag.\n              </video>\n            </div>\n\n            {/* Video Info */}\n            <div className=\"space-y-4\">\n              {/* Title and Badges */}\n              <div className=\"flex flex-wrap items-start gap-3\">\n                <h1 className=\"text-text text-xl md:text-2xl font-bold flex-1 min-w-0\">\n                  {mockVideo.title}\n                </h1>\n                <div className=\"flex gap-2\">\n                  {mockVideo.isHD && (\n                    <span className=\"bg-primary text-white text-xs font-bold px-2 py-1 rounded\">\n                      HD\n                    </span>\n                  )}\n                  {mockVideo.isPremium && (\n                    <span className=\"bg-warning text-black text-xs font-bold px-2 py-1 rounded\">\n                      PREMIUM\n                    </span>\n                  )}\n                </div>\n              </div>\n\n              {/* Stats and Actions */}\n              <div className=\"flex flex-wrap items-center justify-between gap-4\">\n                <div className=\"flex items-center gap-4 text-text-secondary text-sm\">\n                  <span>{formatViews(mockVideo.views)} views</span>\n                  <span>•</span>\n                  <span>{formatTimeAgo(mockVideo.uploadedAt)}</span>\n                </div>\n\n                <div className=\"flex items-center gap-2\">\n                  {/* Like/Dislike */}\n                  <div className=\"flex items-center bg-background-input rounded-lg overflow-hidden\">\n                    <Button\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      onClick={handleLike}\n                      className={`rounded-none px-4 ${isLiked ? 'text-primary' : 'text-text-secondary'}`}\n                    >\n                      <svg className=\"w-5 h-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path d=\"M7.493 18.75c-.425 0-.82-.236-.975-.632A7.48 7.48 0 016 15.375c0-1.75.599-3.358 1.602-4.634.151-.192.373-.309.6-.397.473-.183.89-.514 1.212-.924a9.042 9.042 0 012.861-2.4c.723-.384 1.35-.956 1.653-1.715a4.498 4.498 0 00.322-1.672V3a.75.75 0 01.75-.75 2.25 2.25 0 012.25 2.25c0 1.152-.26 2.243-.723 3.218-.266.558-.107 1.282.725 1.282h3.126c1.026 0 1.945.694 2.054 1.715.045.422.068.85.068 1.285a11.95 11.95 0 01-2.649 7.521c-.388.482-.987.729-1.605.729H14.23c-.483 0-.964-.078-1.423-.23l-3.114-1.04a4.501 4.501 0 00-1.423-.23h-.777zM2.331 10.977a11.969 11.969 0 00-.831 4.398 12 12 0 00.52 3.507c.26.85 1.084 1.368 1.973 1.368H4.9c.445 0 .72-.498.523-.898a8.963 8.963 0 01-.924-3.977c0-1.708.476-3.305 1.302-4.666.245-.403-.028-.959-.5-.959H4.25c-.832 0-1.612.453-1.918 1.227z\"/>\n                      </svg>\n                      {mockVideo.likes + (isLiked ? 1 : 0)}\n                    </Button>\n                    <div className=\"w-px h-6 bg-border\" />\n                    <Button\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      onClick={handleDislike}\n                      className={`rounded-none px-4 ${isDisliked ? 'text-primary' : 'text-text-secondary'}`}\n                    >\n                      <svg className=\"w-5 h-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path d=\"M15.73 5.25h1.035A7.465 7.465 0 0118 9.375a7.465 7.465 0 01-1.235 4.125h-.148c-.806 0-1.534.446-2.031 1.08a9.04 9.04 0 01-2.861 2.4c-.723.384-1.35.956-1.653 1.715a4.498 4.498 0 00-.322 1.672V21a.75.75 0 01-.75.75 2.25 2.25 0 01-2.25-2.25c0-1.152.26-2.243.723-3.218C7.74 15.724 7.366 15 6.748 15H3.622c-1.026 0-1.945-.694-2.054-1.715A12.134 12.134 0 011.5 12c0-2.848.992-5.464 2.649-7.521C4.537 3.997 5.136 3.75 5.754 3.75h9.776c.483 0 .964.078 1.423.23l3.114 1.04a4.501 4.501 0 001.423.23h.777zM21.669 13.023c.536-1.362.831-2.845.831-4.398 0-1.22-.182-2.398-.52-3.507-.26-.85-1.084-1.368-1.973-1.368H19.1c-.445 0-.72.498-.523.898.591 1.2.924 2.55.924 3.977a8.958 8.958 0 01-1.302 4.666c-.245.403.028.959.5.959h1.053c.832 0 1.612-.453 1.918-1.227z\"/>\n                      </svg>\n                      {mockVideo.dislikes + (isDisliked ? 1 : 0)}\n                    </Button>\n                  </div>\n\n                  {/* Favorite */}\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={() => setIsFavorited(!isFavorited)}\n                    className={`${isFavorited ? 'text-primary' : 'text-text-secondary'}`}\n                  >\n                    <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path d=\"M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z\"/>\n                    </svg>\n                  </Button>\n\n                  {/* Share */}\n                  <Button variant=\"ghost\" size=\"sm\" className=\"text-text-secondary\">\n                    <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path d=\"M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .***********.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92 1.61 0 2.92-1.31 2.92-2.92s-1.31-2.92-2.92-2.92z\"/>\n                    </svg>\n                  </Button>\n                </div>\n              </div>\n\n              {/* Uploader Info */}\n              <div className=\"flex items-center justify-between p-4 bg-background-card rounded-lg\">\n                <div className=\"flex items-center gap-3\">\n                  <div\n                    className=\"w-12 h-12 rounded-full bg-cover bg-center border-2 border-primary\"\n                    style={{ backgroundImage: `url(\"${mockVideo.uploader.avatar}\")` }}\n                  />\n                  <div>\n                    <div className=\"flex items-center gap-2\">\n                      <h3 className=\"text-text font-semibold\">{mockVideo.uploader.name}</h3>\n                      {mockVideo.uploader.verified && (\n                        <svg className=\"w-4 h-4 text-primary\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\n                        </svg>\n                      )}\n                    </div>\n                    <p className=\"text-text-secondary text-sm\">\n                      {mockVideo.uploader.subscribers.toLocaleString()} subscribers\n                    </p>\n                  </div>\n                </div>\n                <Button variant=\"primary\" size=\"sm\">\n                  Subscribe\n                </Button>\n              </div>\n\n              {/* Description */}\n              <div className=\"bg-background-card rounded-lg p-4\">\n                <div className=\"flex items-center justify-between mb-3\">\n                  <h3 className=\"text-text font-semibold\">Description</h3>\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={() => setShowDescription(!showDescription)}\n                  >\n                    {showDescription ? 'Show less' : 'Show more'}\n                  </Button>\n                </div>\n                <p className={`text-text-secondary text-sm leading-relaxed ${\n                  showDescription ? '' : 'line-clamp-3'\n                }`}>\n                  {mockVideo.description}\n                </p>\n                \n                {/* Tags */}\n                <div className=\"flex flex-wrap gap-2 mt-4\">\n                  {mockVideo.tags.map((tag) => (\n                    <span\n                      key={tag}\n                      className=\"bg-background-input text-text-secondary text-xs px-2 py-1 rounded hover:bg-background-hover transition-colors cursor-pointer\"\n                    >\n                      #{tag}\n                    </span>\n                  ))}\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Sidebar - Related Videos */}\n          <div className=\"lg:w-80 xl:w-96\">\n            <h2 className=\"text-text text-lg font-bold mb-4\">Related Videos</h2>\n            <div className=\"space-y-4\">\n              {relatedVideos.map((video) => (\n                <div key={video.id} className=\"flex gap-3\">\n                  <div className=\"w-32 flex-shrink-0\">\n                    <VideoCard\n                      id={video.id}\n                      title={video.title}\n                      thumbnail={video.thumbnail}\n                      duration={video.duration}\n                      views={video.views}\n                      uploadedAt={video.uploadedAt}\n                      category={video.category}\n                      isHD={video.isHD}\n                      isPremium={video.isPremium}\n                      className=\"scale-75 origin-top-left\"\n                    />\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    </MainLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASA,kBAAkB;AAClB,MAAM,YAAY;IAChB,IAAI;IACJ,OAAO;IACP,aAAa;IACb,UAAU;IACV,OAAO;IACP,OAAO;IACP,UAAU;IACV,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK;IACrD,UAAU;IACV,MAAM;QAAC;QAAc;QAAY;QAAM;QAAY;KAAS;IAC5D,MAAM;IACN,WAAW;IACX,UAAU;IACV,WAAW;IACX,UAAU;QACR,MAAM;QACN,QAAQ;QACR,UAAU;QACV,aAAa;IACf;AACF;AAEA,sBAAsB;AACtB,MAAM,gBAAgB;IACpB;QACE,IAAI;QACJ,OAAO;QACP,WAAW;QACX,UAAU;QACV,OAAO;QACP,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK;QACrD,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,OAAO;QACP,WAAW;QACX,UAAU;QACV,OAAO;QACP,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK;QACrD,UAAU;QACV,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,WAAW;QACX,UAAU;QACV,OAAO;QACP,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK;QACtD,UAAU;IACZ;CACD;AAEc,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,aAAa;QACjB,WAAW,CAAC;QACZ,IAAI,YAAY,cAAc;IAChC;IAEA,MAAM,gBAAgB;QACpB,cAAc,CAAC;QACf,IAAI,SAAS,WAAW;IAC1B;IAEA,qBACE,6LAAC,6IAAA,CAAA,aAAU;kBACT,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,WAAU;oCACV,QAAQ;oCACR,QAAQ,UAAU,SAAS;oCAC3B,SAAQ;;sDAER,6LAAC;4CAAO,KAAK,UAAU,QAAQ;4CAAE,MAAK;;;;;;wCAAc;;;;;;;;;;;;0CAMxD,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DACX,UAAU,KAAK;;;;;;0DAElB,6LAAC;gDAAI,WAAU;;oDACZ,UAAU,IAAI,kBACb,6LAAC;wDAAK,WAAU;kEAA4D;;;;;;oDAI7E,UAAU,SAAS,kBAClB,6LAAC;wDAAK,WAAU;kEAA4D;;;;;;;;;;;;;;;;;;kDAQlF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;4DAAM,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,UAAU,KAAK;4DAAE;;;;;;;kEACpC,6LAAC;kEAAK;;;;;;kEACN,6LAAC;kEAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,UAAU,UAAU;;;;;;;;;;;;0DAG3C,6LAAC;gDAAI,WAAU;;kEAEb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS;gEACT,WAAW,CAAC,kBAAkB,EAAE,UAAU,iBAAiB,uBAAuB;;kFAElF,6LAAC;wEAAI,WAAU;wEAAe,MAAK;wEAAe,SAAQ;kFACxD,cAAA,6LAAC;4EAAK,GAAE;;;;;;;;;;;oEAET,UAAU,KAAK,GAAG,CAAC,UAAU,IAAI,CAAC;;;;;;;0EAErC,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS;gEACT,WAAW,CAAC,kBAAkB,EAAE,aAAa,iBAAiB,uBAAuB;;kFAErF,6LAAC;wEAAI,WAAU;wEAAe,MAAK;wEAAe,SAAQ;kFACxD,cAAA,6LAAC;4EAAK,GAAE;;;;;;;;;;;oEAET,UAAU,QAAQ,GAAG,CAAC,aAAa,IAAI,CAAC;;;;;;;;;;;;;kEAK7C,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,eAAe,CAAC;wDAC/B,WAAW,GAAG,cAAc,iBAAiB,uBAAuB;kEAEpE,cAAA,6LAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAe,SAAQ;sEACnD,cAAA,6LAAC;gEAAK,GAAE;;;;;;;;;;;;;;;;kEAKZ,6LAAC,qIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAQ,MAAK;wDAAK,WAAU;kEAC1C,cAAA,6LAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAe,SAAQ;sEACnD,cAAA,6LAAC;gEAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOhB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,iBAAiB,CAAC,KAAK,EAAE,UAAU,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;wDAAC;;;;;;kEAElE,6LAAC;;0EACC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;kFAA2B,UAAU,QAAQ,CAAC,IAAI;;;;;;oEAC/D,UAAU,QAAQ,CAAC,QAAQ,kBAC1B,6LAAC;wEAAI,WAAU;wEAAuB,MAAK;wEAAe,SAAQ;kFAChE,cAAA,6LAAC;4EAAK,GAAE;;;;;;;;;;;;;;;;;0EAId,6LAAC;gEAAE,WAAU;;oEACV,UAAU,QAAQ,CAAC,WAAW,CAAC,cAAc;oEAAG;;;;;;;;;;;;;;;;;;;0DAIvD,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;0DAAK;;;;;;;;;;;;kDAMtC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA0B;;;;;;kEACxC,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,mBAAmB,CAAC;kEAElC,kBAAkB,cAAc;;;;;;;;;;;;0DAGrC,6LAAC;gDAAE,WAAW,CAAC,4CAA4C,EACzD,kBAAkB,KAAK,gBACvB;0DACC,UAAU,WAAW;;;;;;0DAIxB,6LAAC;gDAAI,WAAU;0DACZ,UAAU,IAAI,CAAC,GAAG,CAAC,CAAC,oBACnB,6LAAC;wDAEC,WAAU;;4DACX;4DACG;;uDAHG;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAYjB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAC,sBAClB,6LAAC;wCAAmB,WAAU;kDAC5B,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,wIAAA,CAAA,YAAS;gDACR,IAAI,MAAM,EAAE;gDACZ,OAAO,MAAM,KAAK;gDAClB,WAAW,MAAM,SAAS;gDAC1B,UAAU,MAAM,QAAQ;gDACxB,OAAO,MAAM,KAAK;gDAClB,YAAY,MAAM,UAAU;gDAC5B,UAAU,MAAM,QAAQ;gDACxB,MAAM,MAAM,IAAI;gDAChB,WAAW,MAAM,SAAS;gDAC1B,WAAU;;;;;;;;;;;uCAZN,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBlC;GAzMwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}]}