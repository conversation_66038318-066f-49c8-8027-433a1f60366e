(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/category/MobileMenu.tsx [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_components_category_MobileMenu_tsx_3b964686._.js",
  "static/chunks/src_components_category_MobileMenu_tsx_61e941c2._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/category/MobileMenu.tsx [app-client] (ecmascript)");
    });
});
}}),
}]);