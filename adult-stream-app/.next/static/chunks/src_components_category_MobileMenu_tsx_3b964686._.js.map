{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/release/adult-stream-app/src/components/category/MobileMenu.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\n\ninterface MobileMenuProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nexport function MobileMenu({ isOpen, onClose }: MobileMenuProps) {\n  if (!isOpen) return null;\n\n  return (\n    <>\n      {/* Backdrop */}\n      <div \n        className=\"fixed inset-0 bg-black/50 z-40 md:hidden\"\n        onClick={onClose}\n      />\n      \n      {/* Menu Panel */}\n      <div className=\"fixed top-0 right-0 h-full w-64 bg-[#1A0B0C] border-l border-[#391D1F] z-50 md:hidden\">\n        <div className=\"flex flex-col h-full\">\n          {/* Header */}\n          <div className=\"flex items-center justify-between p-4 border-b border-[#391D1F]\">\n            <h3 className=\"text-white font-semibold\">Menu</h3>\n            <button\n              onClick={onClose}\n              className=\"text-gray-400 hover:text-white p-2\"\n              aria-label=\"Close menu\"\n            >\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n          </div>\n\n          {/* Navigation Links */}\n          <nav className=\"flex-1 p-4\">\n            <div className=\"space-y-4\">\n              <Link \n                href=\"/\"\n                className=\"block text-gray-300 hover:text-white py-2 transition-colors\"\n                onClick={onClose}\n              >\n                Home\n              </Link>\n              <Link \n                href=\"/categories\"\n                className=\"block text-gray-300 hover:text-white py-2 transition-colors\"\n                onClick={onClose}\n              >\n                Categories\n              </Link>\n              <Link \n                href=\"#\"\n                className=\"block text-gray-300 hover:text-white py-2 transition-colors\"\n                onClick={onClose}\n              >\n                Models\n              </Link>\n              <Link \n                href=\"#\"\n                className=\"block text-gray-300 hover:text-white py-2 transition-colors\"\n                onClick={onClose}\n              >\n                Premium\n              </Link>\n              \n              {/* Divider */}\n              <div className=\"border-t border-[#391D1F] my-4\" />\n              \n              <Link \n                href=\"#\"\n                className=\"block text-gray-300 hover:text-white py-2 transition-colors\"\n                onClick={onClose}\n              >\n                Favorites\n              </Link>\n              <Link \n                href=\"#\"\n                className=\"block text-gray-300 hover:text-white py-2 transition-colors\"\n                onClick={onClose}\n              >\n                Watch Later\n              </Link>\n              <Link \n                href=\"#\"\n                className=\"block text-gray-300 hover:text-white py-2 transition-colors\"\n                onClick={onClose}\n              >\n                History\n              </Link>\n            </div>\n          </nav>\n\n          {/* Footer */}\n          <div className=\"p-4 border-t border-[#391D1F]\">\n            <button className=\"w-full bg-[#E92933] hover:bg-[#d9202a] text-white font-semibold py-2 px-4 rounded-lg transition-colors\">\n              Sign In\n            </button>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAUO,SAAS,WAAW,EAAE,MAAM,EAAE,OAAO,EAAmB;IAC7D,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE;;0BAEE,6LAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAIX,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA2B;;;;;;8CACzC,6LAAC;oCACC,SAAS;oCACT,WAAU;oCACV,cAAW;8CAEX,cAAA,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;sCAM3E,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS;kDACV;;;;;;kDAGD,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS;kDACV;;;;;;kDAGD,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS;kDACV;;;;;;kDAGD,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS;kDACV;;;;;;kDAKD,6LAAC;wCAAI,WAAU;;;;;;kDAEf,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS;kDACV;;;;;;kDAGD,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS;kDACV;;;;;;kDAGD,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS;kDACV;;;;;;;;;;;;;;;;;sCAOL,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAO,WAAU;0CAAyG;;;;;;;;;;;;;;;;;;;;;;;;AAQvI;KAjGgB", "debugId": null}}]}